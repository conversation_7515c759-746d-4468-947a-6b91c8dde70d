# ML Utilities Package

This package consolidates all core machine learning functionality for the ML Log Prediction system into a well-organized, maintainable structure.

## 📁 Package Structure

```
utils/ml/
├── __init__.py                  # Main package interface
├── algorithms/                  # Core ML algorithms
│   ├── __init__.py
│   ├── regression.py           # Multiple Linear Regression utilities
│   ├── metrics.py              # Evaluation metrics (R², MAE, RMSE, etc.)
│   └── preprocessing.py        # Advanced preprocessing (planned)
├── optimization/               # Performance optimization
│   ├── __init__.py
│   ├── gpu_utils.py           # GPU acceleration and CUDA utilities
│   ├── memory.py              # Memory optimization (planned)
│   └── performance.py         # Performance monitoring (planned)
└── integration/               # Advanced integrations
    ├── __init__.py
    └── phase1_core.py         # Phase 1 ML enhancements (planned)
```

## 🚀 Quick Start

### Basic Usage

```python
# Import consolidated utilities
from utils.ml.algorithms import MLRModelWrapper, cal_r2, cal_cc
from utils.ml.optimization import GPUAccelerator

# Create and use MLR model
model = MLRModelWrapper(model_type='ridge', enable_diagnostics=True)
model.fit(X_train, y_train)
predictions = model.predict(X_test)

# Use GPU acceleration
gpu = GPUAccelerator()
optimized_model = gpu.optimize_model_for_gpu(pytorch_model)

# Calculate metrics
r2_score = cal_r2(predictions, y_true)
```

### Package-Level Imports

```python
# Import from package level for convenience
from utils.ml import MLRModelWrapper, GPUAccelerator
```

## 📊 Available Components

### Algorithms (`utils.ml.algorithms`)

#### Regression Models
- **MLRModelWrapper**: Unified interface for Linear, Ridge, Lasso, ElasticNet
- **MLRPreprocessor**: Automated preprocessing with outlier detection and VIF analysis
- **create_mlr_model()**: Factory function for creating MLR models
- **validate_mlr_assumptions()**: Linear regression assumption validation

#### Evaluation Metrics
- **cal_r2()**: R-squared calculation with mask support
- **cal_cc()**: Correlation coefficient calculation
- **evaluate_model_comprehensive()**: Comprehensive model evaluation
- **calculate_prediction_metrics()**: Standard prediction metrics

### Optimization (`utils.ml.optimization`)

#### GPU Utilities
- **GPUAccelerator**: GPU detection, memory management, model optimization
- **get_gpu_accelerator()**: Factory function for GPU accelerator
- **safe_cuda_empty_cache()**: Safe CUDA cache clearing

## 🔄 Migration from Old Structure

The new structure maintains backward compatibility while providing better organization:

### Old vs New Imports

| Old Import | New Import |
|------------|------------|
| `from utils.metrics import cal_r2` | `from utils.ml.algorithms.metrics import cal_r2` |
| `from utils.optimization import GPUAccelerator` | `from utils.ml.optimization.gpu_utils import GPUAccelerator` |
| `from utils.mlr_utils import MLRModelWrapper` | `from utils.ml.algorithms.regression import MLRModelWrapper` |

### Backward Compatibility

Old imports still work but show deprecation warnings:

```python
# Still works but deprecated
from utils.metrics import cal_r2  # ⚠️ DeprecationWarning
```

## 🎯 Benefits

1. **Better Organization**: Related functionality grouped together
2. **Clear Dependencies**: Easy to understand component relationships
3. **Easier Maintenance**: Centralized location for each utility type
4. **Better Documentation**: Each package has clear purpose and interface
5. **Future Extensibility**: Easy to add new algorithms and optimizations
6. **Backward Compatibility**: Existing code continues to work

## 🔧 Development

### Adding New Components

1. **New Algorithm**: Add to `algorithms/` subdirectory
2. **New Optimization**: Add to `optimization/` subdirectory
3. **New Integration**: Add to `integration/` subdirectory

### Testing

```python
# Test imports work correctly
from utils.ml.algorithms import MLRModelWrapper
from utils.ml.optimization import GPUAccelerator

# Test functionality
model = MLRModelWrapper()
gpu = GPUAccelerator()
print("✅ ML utilities package working correctly")
```

## 📈 Status

### ✅ Completed
- Package structure created
- Metrics utilities migrated
- GPU utilities migrated
- MLR utilities migrated
- Backward compatibility implemented
- Documentation created

### 📋 Planned
- Advanced preprocessing utilities migration
- Memory optimization utilities
- Performance monitoring utilities
- Phase 1 integration migration
- Complete deprecation of old imports (future release)

## 🤝 Contributing

When adding new ML utilities:

1. Choose the appropriate subdirectory (`algorithms/`, `optimization/`, `integration/`)
2. Follow the existing code style and documentation patterns
3. Add appropriate imports to `__init__.py` files
4. Update this README with new components
5. Add tests to verify functionality

## 📚 See Also

- [Migration Guide](MIGRATION_GUIDE.md) - Detailed migration instructions
- [utils/ml_core.py](../ml_core.py) - Main ML orchestration hub
- [models/](../../models/) - Advanced model implementations
