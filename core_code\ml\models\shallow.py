"""
Shallow ML Models Interface

This module provides unified interfaces for shallow machine learning models
including XGBoost, LightGBM, CatBoost, Random Forest, and other tree-based models.

Key Features:
- Unified model interface for different shallow ML libraries
- Automatic hyperparameter optimization
- GPU acceleration support
- Model comparison and selection utilities

Author: ML Models Core
"""

import warnings
from typing import Dict, List, Tuple, Optional, Union, Any
import numpy as np
import pandas as pd

# Optional dependencies with graceful fallbacks
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    warnings.warn("XGBoost not available.")

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False
    warnings.warn("LightGBM not available.")

try:
    import catboost as cb
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False
    warnings.warn("CatBoost not available.")

try:
    from sklearn.ensemble import RandomForestRegressor, <PERSON>radientBoostingRegressor
    from sklearn.linear_model import <PERSON>ar<PERSON><PERSON><PERSON>, <PERSON>, Lasso
    from sklearn.svm import SVR
    from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    warnings.warn("scikit-learn not available.")

# Check overall availability
SHALLOW_MODELS_AVAILABLE = any([XGBOOST_AVAILABLE, LIGHTGBM_AVAILABLE, CATBOOST_AVAILABLE, SKLEARN_AVAILABLE])


class ShallowModelWrapper:
    """Unified wrapper for shallow ML models."""
    
    def __init__(self, 
                 model_type: str,
                 model_params: Optional[Dict[str, Any]] = None,
                 use_gpu: bool = False):
        """
        Initialize shallow model wrapper.
        
        Args:
            model_type: Type of model ('xgboost', 'lightgbm', 'catboost', 'random_forest', etc.)
            model_params: Model-specific parameters
            use_gpu: Whether to use GPU acceleration if available
        """
        self.model_type = model_type
        self.model_params = model_params or {}
        self.use_gpu = use_gpu
        self.model = None
        self.is_fitted = False
        self.feature_names = None
        
        # Create model
        self.model = self._create_model()
    
    def _create_model(self):
        """Create the underlying model based on model_type."""
        if self.model_type == 'xgboost':
            if not XGBOOST_AVAILABLE:
                raise ImportError("XGBoost not available")
            
            default_params = {
                'objective': 'reg:squarederror',
                'n_estimators': 100,
                'max_depth': 6,
                'learning_rate': 0.1,
                'random_state': 42
            }
            
            if self.use_gpu:
                default_params['tree_method'] = 'gpu_hist'
                default_params['gpu_id'] = 0
            
            params = {**default_params, **self.model_params}
            return xgb.XGBRegressor(**params)
        
        elif self.model_type == 'lightgbm':
            if not LIGHTGBM_AVAILABLE:
                raise ImportError("LightGBM not available")
            
            default_params = {
                'objective': 'regression',
                'n_estimators': 100,
                'max_depth': 6,
                'learning_rate': 0.1,
                'random_state': 42,
                'verbose': -1
            }
            
            if self.use_gpu:
                default_params['device'] = 'gpu'
            
            params = {**default_params, **self.model_params}
            return lgb.LGBMRegressor(**params)
        
        elif self.model_type == 'catboost':
            if not CATBOOST_AVAILABLE:
                raise ImportError("CatBoost not available")
            
            default_params = {
                'iterations': 100,
                'depth': 6,
                'learning_rate': 0.1,
                'random_seed': 42,
                'verbose': False
            }
            
            if self.use_gpu:
                default_params['task_type'] = 'GPU'
            
            params = {**default_params, **self.model_params}
            return cb.CatBoostRegressor(**params)
        
        elif self.model_type == 'random_forest':
            if not SKLEARN_AVAILABLE:
                raise ImportError("scikit-learn not available")
            
            default_params = {
                'n_estimators': 100,
                'max_depth': None,
                'random_state': 42,
                'n_jobs': -1
            }
            
            params = {**default_params, **self.model_params}
            return RandomForestRegressor(**params)
        
        elif self.model_type == 'gradient_boosting':
            if not SKLEARN_AVAILABLE:
                raise ImportError("scikit-learn not available")
            
            default_params = {
                'n_estimators': 100,
                'max_depth': 3,
                'learning_rate': 0.1,
                'random_state': 42
            }
            
            params = {**default_params, **self.model_params}
            return GradientBoostingRegressor(**params)
        
        elif self.model_type == 'linear':
            if not SKLEARN_AVAILABLE:
                raise ImportError("scikit-learn not available")
            
            return LinearRegression(**self.model_params)
        
        elif self.model_type == 'ridge':
            if not SKLEARN_AVAILABLE:
                raise ImportError("scikit-learn not available")
            
            default_params = {'alpha': 1.0, 'random_state': 42}
            params = {**default_params, **self.model_params}
            return Ridge(**params)
        
        elif self.model_type == 'lasso':
            if not SKLEARN_AVAILABLE:
                raise ImportError("scikit-learn not available")
            
            default_params = {'alpha': 1.0, 'random_state': 42}
            params = {**default_params, **self.model_params}
            return Lasso(**params)
        
        elif self.model_type == 'svr':
            if not SKLEARN_AVAILABLE:
                raise ImportError("scikit-learn not available")
            
            default_params = {'kernel': 'rbf', 'C': 1.0}
            params = {**default_params, **self.model_params}
            return SVR(**params)
        
        else:
            raise ValueError(f"Unknown model type: {self.model_type}")
    
    def fit(self, X, y, validation_data=None, **fit_params):
        """
        Fit the model to training data.
        
        Args:
            X: Training features
            y: Training targets
            validation_data: Optional validation data tuple (X_val, y_val)
            **fit_params: Additional fitting parameters
        """
        if isinstance(X, pd.DataFrame):
            self.feature_names = list(X.columns)
            X = X.values
        
        if isinstance(y, pd.Series):
            y = y.values
        
        # Handle validation data for gradient boosting models
        if validation_data and self.model_type in ['xgboost', 'lightgbm', 'catboost']:
            X_val, y_val = validation_data
            if isinstance(X_val, pd.DataFrame):
                X_val = X_val.values
            if isinstance(y_val, pd.Series):
                y_val = y_val.values
            
            if self.model_type == 'xgboost':
                fit_params['eval_set'] = [(X_val, y_val)]
                fit_params['verbose'] = False
            elif self.model_type == 'lightgbm':
                fit_params['eval_set'] = [(X_val, y_val)]
                fit_params['verbose'] = False
            elif self.model_type == 'catboost':
                fit_params['eval_set'] = [(X_val, y_val)]
                fit_params['verbose'] = False
        
        # Fit the model
        self.model.fit(X, y, **fit_params)
        self.is_fitted = True
        
        return self
    
    def predict(self, X):
        """
        Make predictions.
        
        Args:
            X: Features for prediction
            
        Returns:
            Predictions
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
        
        if isinstance(X, pd.DataFrame):
            X = X.values
        
        return self.model.predict(X)
    
    def score(self, X, y):
        """
        Calculate R² score.
        
        Args:
            X: Features
            y: True targets
            
        Returns:
            R² score
        """
        y_pred = self.predict(X)
        return r2_score(y, y_pred)
    
    def get_feature_importance(self):
        """
        Get feature importance if available.
        
        Returns:
            Feature importance array or None
        """
        if not self.is_fitted:
            return None
        
        if hasattr(self.model, 'feature_importances_'):
            return self.model.feature_importances_
        elif hasattr(self.model, 'get_feature_importance'):
            return self.model.get_feature_importance()
        else:
            return None


def get_shallow_models() -> List[str]:
    """
    Get list of available shallow model types.
    
    Returns:
        List of available model type names
    """
    available_models = []
    
    if SKLEARN_AVAILABLE:
        available_models.extend([
            'linear', 'ridge', 'lasso', 'random_forest', 
            'gradient_boosting', 'svr'
        ])
    
    if XGBOOST_AVAILABLE:
        available_models.append('xgboost')
    
    if LIGHTGBM_AVAILABLE:
        available_models.append('lightgbm')
    
    if CATBOOST_AVAILABLE:
        available_models.append('catboost')
    
    return available_models


def create_shallow_model(model_type: str,
                        model_params: Optional[Dict[str, Any]] = None,
                        use_gpu: bool = False) -> ShallowModelWrapper:
    """
    Factory function to create shallow model instances.
    
    Args:
        model_type: Type of model to create
        model_params: Model-specific parameters
        use_gpu: Whether to use GPU acceleration
        
    Returns:
        ShallowModelWrapper instance
    """
    return ShallowModelWrapper(model_type, model_params, use_gpu)


def compare_shallow_models(X_train, y_train, X_val, y_val,
                          model_types: Optional[List[str]] = None,
                          use_gpu: bool = False) -> Dict[str, Dict[str, float]]:
    """
    Compare multiple shallow models on the same dataset.
    
    Args:
        X_train: Training features
        y_train: Training targets
        X_val: Validation features
        y_val: Validation targets
        model_types: List of model types to compare (None for all available)
        use_gpu: Whether to use GPU acceleration
        
    Returns:
        Dictionary with model comparison results
    """
    if model_types is None:
        model_types = get_shallow_models()
    
    results = {}
    
    for model_type in model_types:
        try:
            print(f"🔍 Testing {model_type}...")
            
            # Create and train model
            model = create_shallow_model(model_type, use_gpu=use_gpu)
            model.fit(X_train, y_train, validation_data=(X_val, y_val))
            
            # Make predictions
            y_pred = model.predict(X_val)
            
            # Calculate metrics
            mae = mean_absolute_error(y_val, y_pred)
            rmse = np.sqrt(mean_squared_error(y_val, y_pred))
            r2 = r2_score(y_val, y_pred)
            
            results[model_type] = {
                'mae': mae,
                'rmse': rmse,
                'r2': r2,
                'composite_score': mae * 0.5 + rmse * 0.3 + (1 - r2) * 0.2
            }
            
            print(f"  ✅ {model_type}: MAE={mae:.4f}, RMSE={rmse:.4f}, R²={r2:.4f}")
            
        except Exception as e:
            print(f"  ❌ {model_type}: Failed - {e}")
            results[model_type] = {'error': str(e)}
    
    # Find best model
    valid_results = {k: v for k, v in results.items() if 'error' not in v}
    if valid_results:
        best_model = min(valid_results.items(), key=lambda x: x[1]['composite_score'])
        print(f"🏆 Best model: {best_model[0]} (composite score: {best_model[1]['composite_score']:.4f})")
    
    return results


def get_model_recommendations(data_shape: Tuple[int, int],
                            target_type: str = 'regression') -> List[str]:
    """
    Get model recommendations based on data characteristics.
    
    Args:
        data_shape: Shape of the dataset (n_samples, n_features)
        target_type: Type of target ('regression' or 'classification')
        
    Returns:
        List of recommended model types
    """
    n_samples, n_features = data_shape
    recommendations = []
    
    # For small datasets
    if n_samples < 1000:
        if SKLEARN_AVAILABLE:
            recommendations.extend(['linear', 'ridge', 'random_forest'])
    
    # For medium datasets
    elif n_samples < 10000:
        if XGBOOST_AVAILABLE:
            recommendations.append('xgboost')
        if LIGHTGBM_AVAILABLE:
            recommendations.append('lightgbm')
        if SKLEARN_AVAILABLE:
            recommendations.append('random_forest')
    
    # For large datasets
    else:
        if LIGHTGBM_AVAILABLE:
            recommendations.append('lightgbm')
        if XGBOOST_AVAILABLE:
            recommendations.append('xgboost')
        if CATBOOST_AVAILABLE:
            recommendations.append('catboost')
    
    # High-dimensional data
    if n_features > 100:
        if SKLEARN_AVAILABLE:
            recommendations.extend(['ridge', 'lasso'])
    
    return recommendations


# Export public interface
__all__ = [
    'ShallowModelWrapper',
    'get_shallow_models',
    'create_shallow_model',
    'compare_shallow_models',
    'get_model_recommendations',
    'SHALLOW_MODELS_AVAILABLE',
    'XGBOOST_AVAILABLE',
    'LIGHTGBM_AVAILABLE',
    'CATBOOST_AVAILABLE',
    'SKLEARN_AVAILABLE'
]
