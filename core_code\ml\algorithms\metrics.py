"""
Core ML Evaluation Metrics

This module provides comprehensive evaluation metrics for regression, imputation, and prediction tasks.
Moved to core_code/ml/algorithms/metrics.py for better organization.

Key Functions:
- cal_r2: R-squared calculation with mask support
- cal_cc: Correlation coefficient calculation
- evaluate_model_comprehensive: Comprehensive model evaluation for shallow ML models
- calculate_prediction_metrics: Standard prediction metrics calculation
- calculate_composite_score: Composite performance scoring
"""

from typing import Union, Optional, Dict, Any
import numpy as np
import torch
from sklearn import metrics
from sklearn.metrics import mean_absolute_error, r2_score, mean_squared_error
from scipy.spatial import distance


def cal_r2(class_predictions: Union[np.ndarray, torch.Tensor],
           targets: Union[np.ndarray, torch.Tensor],
           masks: Optional[Union[np.ndarray, torch.Tensor]] = None,
          ) -> Union[float, torch.Tensor]:
    """
    Calculate the R-squared Error between ``class_predictions`` and ``targets``.
    ``masks`` can be used for filtering. For values==0 in ``masks``,
    values at their corresponding positions in ``predictions`` will be ignored.

    Args:
        class_predictions: The prediction data to be evaluated.
        targets: The target data for helping evaluate the predictions.
        masks: The masks for filtering the specific values in inputs and target from evaluation.
               When given, only values at corresponding positions where values ==1 in ``masks`` will be used for evaluation.
    
    Returns:
        R-squared score
    """
    R2 = metrics.r2_score(targets.flatten(), class_predictions.flatten(), sample_weight=masks.flatten())
    return R2


def cal_cc(class_predictions: Union[np.ndarray, torch.Tensor],
           targets: Union[np.ndarray, torch.Tensor],
           masks: Optional[Union[np.ndarray, torch.Tensor]] = None,
          ) -> Union[float, torch.Tensor]:
    """
    Calculate the Correlation Distance (=1 - Correlation) between ``class_predictions`` and ``targets``.
    ``masks`` can be used for filtering. For values==0 in ``masks``,
    values at their corresponding positions in ``predictions`` will be ignored.

    Args:
        class_predictions: The prediction data to be evaluated.
        targets: The target data for helping evaluate the predictions.
        masks: The masks for filtering the specific values in inputs and target from evaluation.
               When given, only values at corresponding positions where values ==1 in ``masks`` will be used for evaluation.
    
    Returns:
        Correlation distance
    """
    CC = distance.correlation(targets.flatten(), class_predictions.flatten(), w=masks.flatten())
    return CC


def evaluate_model_comprehensive(model, X_train, y_train, X_val, y_val) -> Dict[str, float]:
    """
    Comprehensive model evaluation for shallow ML models.
    
    Args:
        model: Trained model with predict method
        X_train: Training features
        y_train: Training targets
        X_val: Validation features
        y_val: Validation targets
    
    Returns:
        Dictionary with MAE, RMSE, R2 and composite score
    """
    y_pred = model.predict(X_val)
    mae = mean_absolute_error(y_val, y_pred)
    rmse = np.sqrt(mean_squared_error(y_val, y_pred))
    r2 = r2_score(y_val, y_pred)

    # Handle cases where R² is negative (poor model performance)
    r2_penalty = (1 - r2) if r2 > 0 else (1 + abs(r2))

    # Weighted composite score
    composite = (mae * 0.5) + (rmse * 0.3) + (r2_penalty * 0.2)

    return {
        'mae': mae, 
        'rmse': rmse, 
        'r2': r2, 
        'composite_score': composite
    }


def calculate_prediction_metrics(y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
    """
    Calculate standard prediction metrics.
    
    Args:
        y_true: True values
        y_pred: Predicted values
    
    Returns:
        Dictionary with standard metrics
    """
    # Remove NaN values
    valid_mask = ~np.isnan(y_true) & ~np.isnan(y_pred)
    y_true_clean = y_true[valid_mask]
    y_pred_clean = y_pred[valid_mask]
    
    if len(y_true_clean) == 0:
        return {
            'mae': float('inf'),
            'rmse': float('inf'),
            'r2': -float('inf'),
            'n_samples': 0
        }
    
    mae = mean_absolute_error(y_true_clean, y_pred_clean)
    rmse = np.sqrt(mean_squared_error(y_true_clean, y_pred_clean))
    r2 = r2_score(y_true_clean, y_pred_clean)
    
    return {
        'mae': mae,
        'rmse': rmse,
        'r2': r2,
        'n_samples': len(y_true_clean)
    }


def calculate_composite_score(mae: float, rmse: float, r2: float) -> float:
    """
    Calculate composite performance score.
    
    Args:
        mae: Mean Absolute Error
        rmse: Root Mean Square Error
        r2: R-squared score
    
    Returns:
        Composite score (lower is better)
    """
    # Handle cases where R² is negative (poor model performance)
    r2_penalty = (1 - r2) if r2 > 0 else (1 + abs(r2))
    
    # Weighted composite score
    composite = (mae * 0.5) + (rmse * 0.3) + (r2_penalty * 0.2)
    
    return composite


# Export public interface
__all__ = [
    'cal_r2',
    'cal_cc',
    'evaluate_model_comprehensive',
    'calculate_prediction_metrics',
    'calculate_composite_score'
]
