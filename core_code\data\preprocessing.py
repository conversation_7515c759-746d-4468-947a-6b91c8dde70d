"""
Data Preprocessing Utilities

This module provides comprehensive data preprocessing functionality for well log data.
Includes normalization, sequence creation, missing value handling, and data preparation.

Key Features:
- Data normalization with multiple methods
- Sequence creation for deep learning models
- Missing value introduction for training
- Data preparation for prediction tasks

Author: Data Processing Core
"""

import numpy as np
import pandas as pd
import warnings
from typing import Dict, List, Tuple, Optional, Union, Any
from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler

# Optional dependencies with graceful fallbacks
try:
    from sklearn.preprocessing import QuantileTransformer
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    warnings.warn("scikit-learn not available. Some preprocessing features will be disabled.")


def normalize_data(data: Union[pd.DataFrame, np.ndarray], 
                  method: str = 'standard',
                  feature_columns: Optional[List[str]] = None,
                  return_scaler: bool = False) -> Union[np.ndarray, Tuple[np.ndarray, <PERSON>]]:
    """
    Normalize data using various scaling methods.
    
    Args:
        data: Input data to normalize
        method: Normalization method ('standard', 'robust', 'minmax', 'quantile')
        feature_columns: Specific columns to normalize (for DataFrames)
        return_scaler: Whether to return the fitted scaler
        
    Returns:
        Normalized data, optionally with the fitted scaler
    """
    if not SKLEARN_AVAILABLE:
        print("❌ scikit-learn not available. Cannot perform normalization.")
        return data if not return_scaler else (data, None)
    
    # Convert to numpy array if needed
    if isinstance(data, pd.DataFrame):
        if feature_columns:
            data_to_normalize = data[feature_columns].values
        else:
            data_to_normalize = data.select_dtypes(include=[np.number]).values
    else:
        data_to_normalize = np.array(data)
    
    # Choose scaler based on method
    if method == 'standard':
        scaler = StandardScaler()
    elif method == 'robust':
        scaler = RobustScaler()
    elif method == 'minmax':
        scaler = MinMaxScaler()
    elif method == 'quantile':
        if SKLEARN_AVAILABLE:
            scaler = QuantileTransformer(output_distribution='normal', random_state=42)
        else:
            print("⚠️ QuantileTransformer not available, using StandardScaler instead")
            scaler = StandardScaler()
    else:
        raise ValueError(f"Unknown normalization method: {method}")
    
    # Fit and transform
    normalized_data = scaler.fit_transform(data_to_normalize)
    
    print(f"✅ Data normalized using {method} method")
    
    if return_scaler:
        return normalized_data, scaler
    else:
        return normalized_data


def create_sequences(data: np.ndarray, 
                    sequence_length: int = 64,
                    step_size: int = 1,
                    target_column: Optional[int] = None) -> Tuple[np.ndarray, Optional[np.ndarray]]:
    """
    Create sequences for deep learning models from time series data.
    
    Args:
        data: Input data array (n_samples, n_features)
        sequence_length: Length of each sequence
        step_size: Step size between sequences
        target_column: Index of target column (if creating supervised sequences)
        
    Returns:
        Tuple of (sequences, targets) where targets is None if target_column is None
    """
    if len(data.shape) != 2:
        raise ValueError("Input data must be 2D (n_samples, n_features)")
    
    n_samples, n_features = data.shape
    
    if sequence_length > n_samples:
        raise ValueError(f"Sequence length ({sequence_length}) cannot be greater than data length ({n_samples})")
    
    # Calculate number of sequences
    n_sequences = (n_samples - sequence_length) // step_size + 1
    
    # Create sequences
    sequences = np.zeros((n_sequences, sequence_length, n_features))
    targets = None
    
    if target_column is not None:
        targets = np.zeros((n_sequences, sequence_length))
    
    for i in range(n_sequences):
        start_idx = i * step_size
        end_idx = start_idx + sequence_length
        
        sequences[i] = data[start_idx:end_idx]
        
        if target_column is not None:
            targets[i] = data[start_idx:end_idx, target_column]
    
    print(f"✅ Created {n_sequences} sequences of length {sequence_length}")
    
    if targets is not None:
        return sequences, targets
    else:
        return sequences, None


def introduce_missingness(data: np.ndarray, 
                         missing_rate: float = 0.1,
                         pattern: str = 'random',
                         target_columns: Optional[List[int]] = None,
                         random_seed: Optional[int] = None) -> Tuple[np.ndarray, np.ndarray]:
    """
    Introduce missing values into data for training imputation models.
    
    Args:
        data: Input data array
        missing_rate: Fraction of values to make missing (0.0 to 1.0)
        pattern: Missing pattern ('random', 'block', 'column')
        target_columns: Specific columns to introduce missingness (None for all)
        random_seed: Random seed for reproducibility
        
    Returns:
        Tuple of (data_with_missing, missing_mask)
    """
    if random_seed is not None:
        np.random.seed(random_seed)
    
    data_with_missing = data.copy()
    missing_mask = np.zeros_like(data, dtype=bool)
    
    if target_columns is None:
        target_columns = list(range(data.shape[-1]))
    
    if pattern == 'random':
        # Random missing values
        for col in target_columns:
            if len(data.shape) == 3:  # Sequences
                for seq_idx in range(data.shape[0]):
                    n_missing = int(missing_rate * data.shape[1])
                    missing_indices = np.random.choice(data.shape[1], n_missing, replace=False)
                    data_with_missing[seq_idx, missing_indices, col] = np.nan
                    missing_mask[seq_idx, missing_indices, col] = True
            else:  # 2D data
                n_missing = int(missing_rate * data.shape[0])
                missing_indices = np.random.choice(data.shape[0], n_missing, replace=False)
                data_with_missing[missing_indices, col] = np.nan
                missing_mask[missing_indices, col] = True
    
    elif pattern == 'block':
        # Block missing values
        for col in target_columns:
            if len(data.shape) == 3:  # Sequences
                for seq_idx in range(data.shape[0]):
                    block_size = max(1, int(missing_rate * data.shape[1]))
                    start_idx = np.random.randint(0, data.shape[1] - block_size + 1)
                    end_idx = start_idx + block_size
                    data_with_missing[seq_idx, start_idx:end_idx, col] = np.nan
                    missing_mask[seq_idx, start_idx:end_idx, col] = True
            else:  # 2D data
                block_size = max(1, int(missing_rate * data.shape[0]))
                start_idx = np.random.randint(0, data.shape[0] - block_size + 1)
                end_idx = start_idx + block_size
                data_with_missing[start_idx:end_idx, col] = np.nan
                missing_mask[start_idx:end_idx, col] = True
    
    elif pattern == 'column':
        # Entire columns missing
        n_cols_missing = max(1, int(missing_rate * len(target_columns)))
        missing_cols = np.random.choice(target_columns, n_cols_missing, replace=False)
        
        for col in missing_cols:
            data_with_missing[..., col] = np.nan
            missing_mask[..., col] = True
    
    else:
        raise ValueError(f"Unknown missing pattern: {pattern}")
    
    missing_count = np.sum(missing_mask)
    total_count = np.prod(data.shape)
    actual_missing_rate = missing_count / total_count
    
    print(f"✅ Introduced {missing_count} missing values ({actual_missing_rate:.2%} of data)")
    
    return data_with_missing, missing_mask


def prepare_prediction_data(data: pd.DataFrame,
                          feature_columns: List[str],
                          target_column: str,
                          sequence_length: int = 64,
                          normalization_method: str = 'standard',
                          test_size: float = 0.2,
                          random_seed: Optional[int] = None) -> Dict[str, Any]:
    """
    Prepare data for prediction tasks including train/test split and preprocessing.
    
    Args:
        data: Input DataFrame
        feature_columns: List of feature column names
        target_column: Name of target column
        sequence_length: Length of sequences for deep learning
        normalization_method: Method for data normalization
        test_size: Fraction of data to use for testing
        random_seed: Random seed for reproducibility
        
    Returns:
        Dictionary with prepared data splits and preprocessing info
    """
    if random_seed is not None:
        np.random.seed(random_seed)
    
    # Extract features and target
    X = data[feature_columns].values
    y = data[target_column].values
    
    # Split data
    n_samples = len(data)
    n_test = int(test_size * n_samples)
    n_train = n_samples - n_test
    
    # Use random indices for splitting
    indices = np.random.permutation(n_samples)
    train_indices = indices[:n_train]
    test_indices = indices[n_train:]
    
    X_train, X_test = X[train_indices], X[test_indices]
    y_train, y_test = y[train_indices], y[test_indices]
    
    # Normalize features
    X_train_norm, scaler = normalize_data(X_train, method=normalization_method, return_scaler=True)
    X_test_norm = scaler.transform(X_test)
    
    # Create sequences if needed
    if sequence_length > 1:
        # For sequence data, we need to handle this differently
        # This is a simplified version - in practice, you'd want to maintain temporal order
        print("⚠️ Sequence creation with train/test split needs temporal consideration")
    
    prepared_data = {
        'X_train': X_train_norm,
        'X_test': X_test_norm,
        'y_train': y_train,
        'y_test': y_test,
        'scaler': scaler,
        'feature_columns': feature_columns,
        'target_column': target_column,
        'train_indices': train_indices,
        'test_indices': test_indices,
        'preprocessing_info': {
            'normalization_method': normalization_method,
            'sequence_length': sequence_length,
            'test_size': test_size,
            'n_train': n_train,
            'n_test': n_test
        }
    }
    
    print(f"✅ Data prepared: {n_train} training samples, {n_test} test samples")
    
    return prepared_data


def handle_missing_values(data: np.ndarray,
                         method: str = 'forward_fill',
                         fill_value: Optional[float] = None) -> np.ndarray:
    """
    Handle missing values in data using various strategies.
    
    Args:
        data: Input data with potential missing values
        method: Method for handling missing values ('forward_fill', 'backward_fill', 'mean', 'median', 'constant')
        fill_value: Value to use for 'constant' method
        
    Returns:
        Data with missing values handled
    """
    data_filled = data.copy()
    
    if method == 'forward_fill':
        # Forward fill along the first axis
        mask = np.isnan(data_filled)
        idx = np.where(~mask, np.arange(mask.shape[0])[:, None, None], 0)
        np.maximum.accumulate(idx, axis=0, out=idx)
        data_filled = data_filled[idx, np.arange(idx.shape[1])[None, :, None], np.arange(idx.shape[2])[None, None, :]]
    
    elif method == 'backward_fill':
        # Backward fill along the first axis
        data_filled = np.flip(data_filled, axis=0)
        mask = np.isnan(data_filled)
        idx = np.where(~mask, np.arange(mask.shape[0])[:, None, None], 0)
        np.maximum.accumulate(idx, axis=0, out=idx)
        data_filled = data_filled[idx, np.arange(idx.shape[1])[None, :, None], np.arange(idx.shape[2])[None, None, :]]
        data_filled = np.flip(data_filled, axis=0)
    
    elif method == 'mean':
        # Fill with mean value
        if len(data.shape) == 3:
            for i in range(data.shape[2]):
                feature_data = data[:, :, i]
                mean_val = np.nanmean(feature_data)
                data_filled[:, :, i] = np.where(np.isnan(feature_data), mean_val, feature_data)
        else:
            for i in range(data.shape[1]):
                feature_data = data[:, i]
                mean_val = np.nanmean(feature_data)
                data_filled[:, i] = np.where(np.isnan(feature_data), mean_val, feature_data)
    
    elif method == 'median':
        # Fill with median value
        if len(data.shape) == 3:
            for i in range(data.shape[2]):
                feature_data = data[:, :, i]
                median_val = np.nanmedian(feature_data)
                data_filled[:, :, i] = np.where(np.isnan(feature_data), median_val, feature_data)
        else:
            for i in range(data.shape[1]):
                feature_data = data[:, i]
                median_val = np.nanmedian(feature_data)
                data_filled[:, i] = np.where(np.isnan(feature_data), median_val, feature_data)
    
    elif method == 'constant':
        # Fill with constant value
        if fill_value is None:
            fill_value = 0.0
        data_filled = np.where(np.isnan(data), fill_value, data)
    
    else:
        raise ValueError(f"Unknown missing value handling method: {method}")
    
    missing_count_before = np.sum(np.isnan(data))
    missing_count_after = np.sum(np.isnan(data_filled))
    
    print(f"✅ Missing values handled: {missing_count_before} → {missing_count_after} using {method}")
    
    return data_filled


def calculate_data_statistics(data: np.ndarray,
                            feature_names: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Calculate comprehensive statistics for data.
    
    Args:
        data: Input data array
        feature_names: Optional feature names
        
    Returns:
        Dictionary with data statistics
    """
    stats = {
        'shape': data.shape,
        'total_elements': np.prod(data.shape),
        'missing_values': np.sum(np.isnan(data)),
        'missing_percentage': np.sum(np.isnan(data)) / np.prod(data.shape) * 100,
        'feature_statistics': {}
    }
    
    # Calculate per-feature statistics
    if len(data.shape) == 3:  # Sequences
        n_features = data.shape[2]
        for i in range(n_features):
            feature_data = data[:, :, i]
            feature_name = feature_names[i] if feature_names and i < len(feature_names) else f"feature_{i}"
            
            stats['feature_statistics'][feature_name] = {
                'mean': np.nanmean(feature_data),
                'std': np.nanstd(feature_data),
                'min': np.nanmin(feature_data),
                'max': np.nanmax(feature_data),
                'missing_count': np.sum(np.isnan(feature_data)),
                'missing_percentage': np.sum(np.isnan(feature_data)) / feature_data.size * 100
            }
    
    elif len(data.shape) == 2:  # 2D data
        n_features = data.shape[1]
        for i in range(n_features):
            feature_data = data[:, i]
            feature_name = feature_names[i] if feature_names and i < len(feature_names) else f"feature_{i}"
            
            stats['feature_statistics'][feature_name] = {
                'mean': np.nanmean(feature_data),
                'std': np.nanstd(feature_data),
                'min': np.nanmin(feature_data),
                'max': np.nanmax(feature_data),
                'missing_count': np.sum(np.isnan(feature_data)),
                'missing_percentage': np.sum(np.isnan(feature_data)) / feature_data.size * 100
            }
    
    return stats


# Export public interface
__all__ = [
    'normalize_data',
    'create_sequences',
    'introduce_missingness',
    'prepare_prediction_data',
    'handle_missing_values',
    'calculate_data_statistics',
    'SKLEARN_AVAILABLE'
]
