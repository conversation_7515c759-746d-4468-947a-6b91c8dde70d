"""
Performance Monitoring Utilities

This module provides comprehensive performance monitoring and profiling functionality
for machine learning workflows. Includes timing, throughput analysis, and bottleneck detection.

Key Features:
- Function and model performance profiling
- Throughput and latency measurement
- Bottleneck detection and analysis
- Performance comparison and benchmarking
- Resource utilization monitoring

Author: ML Optimization Core
"""

import time
import functools
import warnings
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
import numpy as np
import psutil

# Optional dependencies with graceful fallbacks
try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    warnings.warn("PyTorch not available. Some performance monitoring features will be disabled.")

try:
    from memory_profiler import profile
    MEMORY_PROFILER_AVAILABLE = True
except ImportError:
    MEMORY_PROFILER_AVAILABLE = False


class PerformanceMonitor:
    """Performance monitoring and profiling utilities."""
    
    def __init__(self):
        """Initialize performance monitor."""
        self.timing_history = {}
        self.performance_metrics = {}
        self.baseline_metrics = None
        
    def time_function(self, func: Callable, *args, **kwargs) -> Tuple[Any, float]:
        """
        Time a function execution.
        
        Args:
            func: Function to time
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Tuple of (function_result, execution_time_seconds)
        """
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        execution_time = end_time - start_time
        
        # Store in history
        func_name = func.__name__ if hasattr(func, '__name__') else str(func)
        if func_name not in self.timing_history:
            self.timing_history[func_name] = []
        self.timing_history[func_name].append(execution_time)
        
        return result, execution_time
    
    def benchmark_model(self, 
                       model: Any,
                       input_data: Any,
                       num_runs: int = 100,
                       warmup_runs: int = 10) -> Dict[str, float]:
        """
        Benchmark model performance.
        
        Args:
            model: Model to benchmark
            input_data: Input data for benchmarking
            num_runs: Number of benchmark runs
            warmup_runs: Number of warmup runs
            
        Returns:
            Benchmark results dictionary
        """
        if not TORCH_AVAILABLE:
            print("⚠️ PyTorch not available. Limited benchmarking capabilities.")
        
        print(f"🏃 Benchmarking model ({warmup_runs} warmup + {num_runs} runs)...")
        
        # Warmup runs
        for _ in range(warmup_runs):
            if TORCH_AVAILABLE and hasattr(model, '__call__'):
                with torch.no_grad():
                    _ = model(input_data)
            else:
                _ = model(input_data)
        
        # Synchronize if using GPU
        if TORCH_AVAILABLE and torch.cuda.is_available():
            torch.cuda.synchronize()
        
        # Benchmark runs
        execution_times = []
        
        for _ in range(num_runs):
            start_time = time.time()
            
            if TORCH_AVAILABLE and hasattr(model, '__call__'):
                with torch.no_grad():
                    _ = model(input_data)
            else:
                _ = model(input_data)
            
            # Synchronize if using GPU
            if TORCH_AVAILABLE and torch.cuda.is_available():
                torch.cuda.synchronize()
            
            end_time = time.time()
            execution_times.append(end_time - start_time)
        
        # Calculate statistics
        execution_times = np.array(execution_times)
        
        benchmark_results = {
            'mean_time_ms': np.mean(execution_times) * 1000,
            'std_time_ms': np.std(execution_times) * 1000,
            'min_time_ms': np.min(execution_times) * 1000,
            'max_time_ms': np.max(execution_times) * 1000,
            'median_time_ms': np.median(execution_times) * 1000,
            'throughput_fps': 1.0 / np.mean(execution_times),
            'num_runs': num_runs,
            'total_time_s': np.sum(execution_times)
        }
        
        print(f"✅ Benchmark completed: {benchmark_results['mean_time_ms']:.2f}ms avg, {benchmark_results['throughput_fps']:.1f} FPS")
        
        return benchmark_results
    
    def profile_data_pipeline(self,
                            pipeline_steps: List[Tuple[str, Callable]],
                            input_data: Any) -> Dict[str, Any]:
        """
        Profile a data processing pipeline.
        
        Args:
            pipeline_steps: List of (step_name, function) tuples
            input_data: Input data for the pipeline
            
        Returns:
            Pipeline profiling results
        """
        print("🔍 Profiling data pipeline...")
        
        step_times = {}
        step_outputs = {}
        current_data = input_data
        total_start_time = time.time()
        
        for step_name, step_function in pipeline_steps:
            step_start_time = time.time()
            
            try:
                current_data = step_function(current_data)
                step_outputs[step_name] = current_data
                
                step_end_time = time.time()
                step_times[step_name] = step_end_time - step_start_time
                
                print(f"  ✅ {step_name}: {step_times[step_name]*1000:.2f}ms")
                
            except Exception as e:
                step_end_time = time.time()
                step_times[step_name] = step_end_time - step_start_time
                print(f"  ❌ {step_name}: Failed after {step_times[step_name]*1000:.2f}ms - {e}")
                break
        
        total_end_time = time.time()
        total_time = total_end_time - total_start_time
        
        # Calculate bottlenecks
        sorted_steps = sorted(step_times.items(), key=lambda x: x[1], reverse=True)
        bottleneck_step = sorted_steps[0] if sorted_steps else None
        
        profiling_results = {
            'step_times': step_times,
            'total_time_s': total_time,
            'bottleneck_step': bottleneck_step[0] if bottleneck_step else None,
            'bottleneck_time_s': bottleneck_step[1] if bottleneck_step else 0,
            'bottleneck_percentage': (bottleneck_step[1] / total_time * 100) if bottleneck_step and total_time > 0 else 0,
            'step_percentages': {step: (time_val / total_time * 100) for step, time_val in step_times.items()} if total_time > 0 else {}
        }
        
        print(f"🎯 Pipeline completed in {total_time*1000:.2f}ms")
        if bottleneck_step:
            print(f"🐌 Bottleneck: {bottleneck_step[0]} ({profiling_results['bottleneck_percentage']:.1f}% of total time)")
        
        return profiling_results
    
    def monitor_resource_usage(self, duration_seconds: float = 60.0, interval_seconds: float = 1.0) -> Dict[str, Any]:
        """
        Monitor system resource usage over time.
        
        Args:
            duration_seconds: Duration to monitor
            interval_seconds: Sampling interval
            
        Returns:
            Resource usage statistics
        """
        print(f"📊 Monitoring resource usage for {duration_seconds}s...")
        
        cpu_usage = []
        memory_usage = []
        gpu_usage = []
        timestamps = []
        
        start_time = time.time()
        
        while time.time() - start_time < duration_seconds:
            current_time = time.time() - start_time
            timestamps.append(current_time)
            
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=None)
            cpu_usage.append(cpu_percent)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_usage.append(memory.percent)
            
            # GPU usage (if available)
            if TORCH_AVAILABLE and torch.cuda.is_available():
                gpu_memory = torch.cuda.memory_allocated() / torch.cuda.get_device_properties(0).total_memory * 100
                gpu_usage.append(gpu_memory)
            
            time.sleep(interval_seconds)
        
        # Calculate statistics
        resource_stats = {
            'duration_s': duration_seconds,
            'samples': len(timestamps),
            'cpu_usage': {
                'mean': np.mean(cpu_usage),
                'max': np.max(cpu_usage),
                'min': np.min(cpu_usage),
                'std': np.std(cpu_usage)
            },
            'memory_usage': {
                'mean': np.mean(memory_usage),
                'max': np.max(memory_usage),
                'min': np.min(memory_usage),
                'std': np.std(memory_usage)
            }
        }
        
        if gpu_usage:
            resource_stats['gpu_usage'] = {
                'mean': np.mean(gpu_usage),
                'max': np.max(gpu_usage),
                'min': np.min(gpu_usage),
                'std': np.std(gpu_usage)
            }
        
        print(f"✅ Resource monitoring completed")
        print(f"   CPU: {resource_stats['cpu_usage']['mean']:.1f}% avg, {resource_stats['cpu_usage']['max']:.1f}% max")
        print(f"   Memory: {resource_stats['memory_usage']['mean']:.1f}% avg, {resource_stats['memory_usage']['max']:.1f}% max")
        
        return resource_stats
    
    def compare_performance(self, 
                          baseline_results: Dict[str, float],
                          current_results: Dict[str, float]) -> Dict[str, Any]:
        """
        Compare current performance with baseline.
        
        Args:
            baseline_results: Baseline performance metrics
            current_results: Current performance metrics
            
        Returns:
            Performance comparison results
        """
        comparison = {
            'improvements': {},
            'regressions': {},
            'overall_change': 0.0,
            'recommendations': []
        }
        
        for metric in baseline_results:
            if metric in current_results:
                baseline_val = baseline_results[metric]
                current_val = current_results[metric]
                
                if baseline_val != 0:
                    change_percent = ((current_val - baseline_val) / baseline_val) * 100
                    
                    if change_percent < -5:  # 5% improvement threshold
                        comparison['improvements'][metric] = {
                            'baseline': baseline_val,
                            'current': current_val,
                            'improvement_percent': abs(change_percent)
                        }
                    elif change_percent > 5:  # 5% regression threshold
                        comparison['regressions'][metric] = {
                            'baseline': baseline_val,
                            'current': current_val,
                            'regression_percent': change_percent
                        }
        
        # Calculate overall performance change
        if baseline_results and current_results:
            # Use execution time as primary metric if available
            time_metrics = ['mean_time_ms', 'execution_time', 'total_time_s']
            primary_metric = None
            
            for metric in time_metrics:
                if metric in baseline_results and metric in current_results:
                    primary_metric = metric
                    break
            
            if primary_metric:
                baseline_val = baseline_results[primary_metric]
                current_val = current_results[primary_metric]
                if baseline_val != 0:
                    comparison['overall_change'] = ((current_val - baseline_val) / baseline_val) * 100
        
        # Generate recommendations
        if comparison['regressions']:
            comparison['recommendations'].append("Performance regressions detected. Investigate recent changes.")
        
        if comparison['improvements']:
            comparison['recommendations'].append("Performance improvements detected. Consider documenting optimizations.")
        
        return comparison
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """
        Get summary of all performance monitoring data.
        
        Returns:
            Performance summary
        """
        summary = {
            'timing_history': {},
            'total_functions_monitored': len(self.timing_history),
            'recommendations': []
        }
        
        # Summarize timing history
        for func_name, times in self.timing_history.items():
            if times:
                summary['timing_history'][func_name] = {
                    'calls': len(times),
                    'mean_time_s': np.mean(times),
                    'total_time_s': np.sum(times),
                    'min_time_s': np.min(times),
                    'max_time_s': np.max(times)
                }
        
        # Find slowest functions
        if summary['timing_history']:
            slowest_func = max(summary['timing_history'].items(), 
                             key=lambda x: x[1]['mean_time_s'])
            summary['slowest_function'] = {
                'name': slowest_func[0],
                'mean_time_s': slowest_func[1]['mean_time_s']
            }
            
            if slowest_func[1]['mean_time_s'] > 1.0:
                summary['recommendations'].append(
                    f"Function '{slowest_func[0]}' is slow ({slowest_func[1]['mean_time_s']:.2f}s avg). Consider optimization."
                )
        
        return summary


def profile_function(func: Callable) -> Callable:
    """
    Decorator to profile function execution time.
    
    Args:
        func: Function to profile
        
    Returns:
        Wrapped function with profiling
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        execution_time = end_time - start_time
        print(f"⏱️ {func.__name__}: {execution_time*1000:.2f}ms")
        
        return result
    
    return wrapper


def benchmark_model(model: Any,
                   input_data: Any,
                   num_runs: int = 100,
                   warmup_runs: int = 10) -> Dict[str, float]:
    """
    Standalone function to benchmark model performance.
    
    Args:
        model: Model to benchmark
        input_data: Input data for benchmarking
        num_runs: Number of benchmark runs
        warmup_runs: Number of warmup runs
        
    Returns:
        Benchmark results
    """
    monitor = PerformanceMonitor()
    return monitor.benchmark_model(model, input_data, num_runs, warmup_runs)


# Export public interface
__all__ = [
    'PerformanceMonitor',
    'profile_function',
    'benchmark_model',
    'TORCH_AVAILABLE',
    'MEMORY_PROFILER_AVAILABLE'
]
