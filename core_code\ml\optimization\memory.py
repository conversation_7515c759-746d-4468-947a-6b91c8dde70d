"""
Memory Optimization Utilities

This module provides comprehensive memory optimization functionality for machine learning models.
Includes memory monitoring, batch size optimization, and gradient checkpointing.

Key Features:
- Memory usage monitoring and profiling
- Dynamic batch size optimization
- Gradient checkpointing for deep models
- Memory-efficient data loading
- Memory leak detection

Author: ML Optimization Core
"""

import gc
import psutil
import warnings
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
import numpy as np

# Optional dependencies with graceful fallbacks
try:
    import torch
    import torch.nn as nn
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    warnings.warn("PyTorch not available. Some memory optimization features will be disabled.")

try:
    from memory_profiler import profile
    MEMORY_PROFILER_AVAILABLE = True
except ImportError:
    MEMORY_PROFILER_AVAILABLE = False
    warnings.warn("memory_profiler not available. Memory profiling will be disabled.")


class MemoryOptimizer:
    """Memory optimization utilities for ML workflows."""
    
    def __init__(self, target_memory_usage: float = 0.8):
        """
        Initialize memory optimizer.
        
        Args:
            target_memory_usage: Target memory usage as fraction of available memory
        """
        self.target_memory_usage = target_memory_usage
        self.memory_history = []
        self.baseline_memory = self.get_memory_usage()
        
    def get_memory_usage(self) -> Dict[str, float]:
        """
        Get current memory usage statistics.
        
        Returns:
            Dictionary with memory usage information
        """
        # System memory
        memory = psutil.virtual_memory()
        
        memory_info = {
            'system_total_gb': memory.total / 1e9,
            'system_available_gb': memory.available / 1e9,
            'system_used_gb': memory.used / 1e9,
            'system_percentage': memory.percent,
            'process_memory_gb': psutil.Process().memory_info().rss / 1e9
        }
        
        # GPU memory if available
        if TORCH_AVAILABLE and torch.cuda.is_available():
            memory_info.update({
                'gpu_allocated_gb': torch.cuda.memory_allocated() / 1e9,
                'gpu_cached_gb': torch.cuda.memory_reserved() / 1e9,
                'gpu_total_gb': torch.cuda.get_device_properties(0).total_memory / 1e9
            })
        
        return memory_info
    
    def monitor_memory(self, operation_name: str = "operation") -> None:
        """
        Monitor memory usage and add to history.
        
        Args:
            operation_name: Name of the operation being monitored
        """
        current_memory = self.get_memory_usage()
        current_memory['operation'] = operation_name
        current_memory['timestamp'] = len(self.memory_history)
        
        self.memory_history.append(current_memory)
        
        # Check for memory issues
        if current_memory['system_percentage'] > 90:
            print(f"⚠️ High memory usage: {current_memory['system_percentage']:.1f}%")
        
        if TORCH_AVAILABLE and torch.cuda.is_available():
            gpu_usage = current_memory['gpu_allocated_gb'] / current_memory['gpu_total_gb'] * 100
            if gpu_usage > 90:
                print(f"⚠️ High GPU memory usage: {gpu_usage:.1f}%")
    
    def clear_memory(self, clear_gpu: bool = True) -> None:
        """
        Clear memory caches and run garbage collection.
        
        Args:
            clear_gpu: Whether to clear GPU memory cache
        """
        # Clear Python garbage
        gc.collect()
        
        # Clear GPU memory if available
        if clear_gpu and TORCH_AVAILABLE and torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        print("🧹 Memory caches cleared")
    
    def optimize_batch_size(self, 
                          model_fn: Callable,
                          sample_input: Any,
                          initial_batch_size: int = 32,
                          max_batch_size: int = 512,
                          memory_limit_gb: Optional[float] = None) -> int:
        """
        Find optimal batch size based on available memory.
        
        Args:
            model_fn: Function that runs the model (should accept batch_size parameter)
            sample_input: Sample input for testing
            initial_batch_size: Starting batch size for testing
            max_batch_size: Maximum batch size to test
            memory_limit_gb: Memory limit in GB (auto-detected if None)
            
        Returns:
            Optimal batch size
        """
        if not TORCH_AVAILABLE:
            print("⚠️ PyTorch not available. Cannot optimize batch size.")
            return initial_batch_size
        
        if memory_limit_gb is None:
            memory_info = self.get_memory_usage()
            if torch.cuda.is_available():
                memory_limit_gb = memory_info['gpu_total_gb'] * self.target_memory_usage
            else:
                memory_limit_gb = memory_info['system_available_gb'] * self.target_memory_usage
        
        print(f"🔍 Optimizing batch size (memory limit: {memory_limit_gb:.1f} GB)...")
        
        optimal_batch_size = initial_batch_size
        current_batch_size = initial_batch_size
        
        while current_batch_size <= max_batch_size:
            try:
                # Clear memory before test
                self.clear_memory()
                
                # Test current batch size
                memory_before = self.get_memory_usage()
                
                # Run model with current batch size
                model_fn(sample_input, batch_size=current_batch_size)
                
                memory_after = self.get_memory_usage()
                
                # Calculate memory usage
                if torch.cuda.is_available():
                    memory_used = memory_after['gpu_allocated_gb']
                else:
                    memory_used = memory_after['process_memory_gb'] - memory_before['process_memory_gb']
                
                if memory_used < memory_limit_gb:
                    optimal_batch_size = current_batch_size
                    print(f"✅ Batch size {current_batch_size}: {memory_used:.2f} GB")
                    current_batch_size *= 2
                else:
                    print(f"❌ Batch size {current_batch_size}: {memory_used:.2f} GB (exceeds limit)")
                    break
                    
            except RuntimeError as e:
                if "out of memory" in str(e).lower():
                    print(f"❌ Batch size {current_batch_size}: Out of memory")
                    break
                else:
                    raise e
        
        print(f"🎯 Optimal batch size: {optimal_batch_size}")
        return optimal_batch_size
    
    def get_memory_report(self) -> Dict[str, Any]:
        """
        Generate comprehensive memory usage report.
        
        Returns:
            Memory usage report
        """
        if not self.memory_history:
            return {'error': 'No memory history available'}
        
        current_memory = self.get_memory_usage()
        
        # Calculate memory growth
        memory_growth = current_memory['process_memory_gb'] - self.baseline_memory['process_memory_gb']
        
        # Find peak memory usage
        peak_memory = max(entry['process_memory_gb'] for entry in self.memory_history)
        
        report = {
            'current_memory': current_memory,
            'baseline_memory': self.baseline_memory,
            'memory_growth_gb': memory_growth,
            'peak_memory_gb': peak_memory,
            'memory_history_length': len(self.memory_history),
            'recommendations': []
        }
        
        # Generate recommendations
        if memory_growth > 1.0:  # More than 1GB growth
            report['recommendations'].append("Significant memory growth detected. Check for memory leaks.")
        
        if current_memory['system_percentage'] > 80:
            report['recommendations'].append("High system memory usage. Consider reducing batch size or model complexity.")
        
        if TORCH_AVAILABLE and torch.cuda.is_available():
            gpu_usage = current_memory['gpu_allocated_gb'] / current_memory['gpu_total_gb'] * 100
            if gpu_usage > 80:
                report['recommendations'].append("High GPU memory usage. Consider gradient checkpointing or smaller models.")
        
        return report


def enable_gradient_checkpointing(model: Any) -> Any:
    """
    Enable gradient checkpointing for memory-efficient training.
    
    Args:
        model: PyTorch model to enable checkpointing for
        
    Returns:
        Model with gradient checkpointing enabled
    """
    if not TORCH_AVAILABLE:
        print("⚠️ PyTorch not available. Cannot enable gradient checkpointing.")
        return model
    
    if hasattr(model, 'gradient_checkpointing_enable'):
        model.gradient_checkpointing_enable()
        print("✅ Gradient checkpointing enabled")
    else:
        print("⚠️ Model does not support gradient checkpointing")
    
    return model


def optimize_batch_size(model: Any,
                       sample_input: Any,
                       initial_batch_size: int = 32,
                       max_batch_size: int = 512) -> int:
    """
    Standalone function to optimize batch size.
    
    Args:
        model: Model to optimize for
        sample_input: Sample input tensor
        initial_batch_size: Starting batch size
        max_batch_size: Maximum batch size to test
        
    Returns:
        Optimal batch size
    """
    optimizer = MemoryOptimizer()
    
    def model_fn(input_data, batch_size):
        if TORCH_AVAILABLE and torch.cuda.is_available():
            model.cuda()
            if hasattr(input_data, 'cuda'):
                input_data = input_data.cuda()
        
        # Create batch
        if hasattr(input_data, 'repeat'):
            batch_input = input_data.repeat(batch_size, *([1] * (input_data.dim() - 1)))
        else:
            batch_input = np.repeat(input_data[np.newaxis, :], batch_size, axis=0)
            if TORCH_AVAILABLE:
                batch_input = torch.tensor(batch_input)
        
        # Forward pass
        with torch.no_grad():
            _ = model(batch_input)
    
    return optimizer.optimize_batch_size(
        model_fn, sample_input, initial_batch_size, max_batch_size
    )


def memory_efficient_data_loader(data: np.ndarray,
                                batch_size: int,
                                shuffle: bool = True,
                                drop_last: bool = False):
    """
    Memory-efficient data loader generator.
    
    Args:
        data: Input data array
        batch_size: Batch size
        shuffle: Whether to shuffle data
        drop_last: Whether to drop last incomplete batch
        
    Yields:
        Batches of data
    """
    n_samples = len(data)
    indices = np.arange(n_samples)
    
    if shuffle:
        np.random.shuffle(indices)
    
    for start_idx in range(0, n_samples, batch_size):
        end_idx = min(start_idx + batch_size, n_samples)
        
        if drop_last and end_idx - start_idx < batch_size:
            break
        
        batch_indices = indices[start_idx:end_idx]
        yield data[batch_indices]


def detect_memory_leaks(memory_history: List[Dict[str, float]],
                       threshold_gb: float = 0.5) -> Dict[str, Any]:
    """
    Detect potential memory leaks from memory history.
    
    Args:
        memory_history: List of memory usage snapshots
        threshold_gb: Threshold for leak detection in GB
        
    Returns:
        Memory leak detection report
    """
    if len(memory_history) < 2:
        return {'error': 'Insufficient memory history for leak detection'}
    
    # Calculate memory growth over time
    memory_values = [entry['process_memory_gb'] for entry in memory_history]
    
    # Simple linear trend analysis
    x = np.arange(len(memory_values))
    coeffs = np.polyfit(x, memory_values, 1)
    growth_rate = coeffs[0]  # GB per operation
    
    leak_report = {
        'growth_rate_gb_per_operation': growth_rate,
        'total_growth_gb': memory_values[-1] - memory_values[0],
        'has_potential_leak': growth_rate > threshold_gb / len(memory_values),
        'recommendations': []
    }
    
    if leak_report['has_potential_leak']:
        leak_report['recommendations'].extend([
            "Potential memory leak detected",
            "Check for unreleased references",
            "Ensure proper cleanup of large objects",
            "Consider using memory profiling tools"
        ])
    
    return leak_report


def get_memory_optimizer() -> MemoryOptimizer:
    """
    Factory function to get memory optimizer instance.
    
    Returns:
        MemoryOptimizer instance
    """
    return MemoryOptimizer()


# Export public interface
__all__ = [
    'MemoryOptimizer',
    'enable_gradient_checkpointing',
    'optimize_batch_size',
    'memory_efficient_data_loader',
    'detect_memory_leaks',
    'get_memory_optimizer',
    'TORCH_AVAILABLE',
    'MEMORY_PROFILER_AVAILABLE'
]
