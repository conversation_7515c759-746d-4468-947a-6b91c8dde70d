"""
GPU Acceleration Utilities

Core GPU acceleration and CUDA utilities for machine learning models.
Moved to core_code/ml/optimization/ for better organization.

Key Features:
- Automatic GPU detection and configuration
- Memory management and optimization
- Model optimization for GPU execution
- Performance monitoring and statistics
"""

import torch
import numpy as np
import gc
from typing import Dict, Any, Optional


class GPUAccelerator:
    """GPU acceleration utilities for deep learning models."""
    
    def __init__(self):
        """Initialize GPU accelerator."""
        self.device = self._detect_best_device()
        self.memory_fraction = 0.8  # Use 80% of GPU memory
        self._setup_gpu_memory()
        
    def _detect_best_device(self) -> torch.device:
        """Detect the best available device."""
        if torch.cuda.is_available():
            device = torch.device('cuda')
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
            print(f"🚀 GPU detected: {gpu_name} ({gpu_memory:.1f} GB)")
            return device
        else:
            print("💻 Using CPU (GPU not available)")
            return torch.device('cpu')
    
    def _setup_gpu_memory(self) -> None:
        """Setup GPU memory management."""
        if self.device.type == 'cuda':
            # Clear cache
            torch.cuda.empty_cache()
            
            # Set memory fraction
            total_memory = torch.cuda.get_device_properties(0).total_memory
            torch.cuda.set_per_process_memory_fraction(self.memory_fraction)
            
            print(f"🔧 GPU memory configured: {self.memory_fraction*100:.0f}% of {total_memory/1e9:.1f} GB")
    
    def optimize_model_for_gpu(self, model: torch.nn.Module) -> torch.nn.Module:
        """
        Optimize model for GPU execution.
        
        Args:
            model: PyTorch model to optimize
            
        Returns:
            Optimized model
        """
        if self.device.type == 'cuda':
            model = model.to(self.device)
            
            # Enable mixed precision if supported
            if hasattr(torch.cuda, 'amp'):
                print("⚡ Mixed precision training enabled")
                model = torch.jit.script(model) if hasattr(torch, 'jit') else model
            
            # Optimize for inference if possible
            model.eval()
            with torch.no_grad():
                # Warm up GPU
                dummy_input = torch.randn(1, 64, 4).to(self.device)
                try:
                    _ = model(dummy_input)
                    print("🔥 GPU warm-up completed")
                except Exception as e:
                    print(f"⚠️ GPU warm-up failed: {e}")
        
        return model
    
    def get_memory_stats(self) -> Dict[str, float]:
        """Get current GPU memory statistics."""
        if self.device.type == 'cuda':
            allocated = torch.cuda.memory_allocated() / 1e9
            cached = torch.cuda.memory_reserved() / 1e9
            total = torch.cuda.get_device_properties(0).total_memory / 1e9
            
            return {
                'allocated_gb': allocated,
                'cached_gb': cached,
                'total_gb': total,
                'utilization_percent': (allocated / total) * 100
            }
        else:
            return {'message': 'GPU not available'}
    
    def clear_memory(self) -> None:
        """Clear GPU memory cache."""
        if self.device.type == 'cuda':
            torch.cuda.empty_cache()
            gc.collect()
            print("🧹 GPU memory cache cleared")
    
    def is_available(self) -> bool:
        """Check if GPU is available."""
        return self.device.type == 'cuda'
    
    def get_device_info(self) -> Dict[str, Any]:
        """Get detailed device information."""
        if self.device.type == 'cuda':
            props = torch.cuda.get_device_properties(0)
            return {
                'name': props.name,
                'total_memory_gb': props.total_memory / 1e9,
                'major': props.major,
                'minor': props.minor,
                'multi_processor_count': props.multi_processor_count,
                'cuda_version': torch.version.cuda,
                'device_count': torch.cuda.device_count()
            }
        else:
            return {
                'name': 'CPU',
                'device_type': 'cpu',
                'cuda_available': False
            }
    
    def optimize_for_inference(self, model: torch.nn.Module) -> torch.nn.Module:
        """
        Optimize model specifically for inference.
        
        Args:
            model: PyTorch model to optimize
            
        Returns:
            Optimized model for inference
        """
        model.eval()
        
        if self.device.type == 'cuda':
            model = model.to(self.device)
            
            # Try to use TorchScript for optimization
            try:
                model = torch.jit.script(model)
                print("📈 TorchScript optimization enabled")
            except Exception as e:
                print(f"⚠️ TorchScript optimization failed: {e}")
        
        return model
    
    def benchmark_model(self, model: torch.nn.Module, input_shape: tuple, 
                       num_runs: int = 100) -> Dict[str, float]:
        """
        Benchmark model performance.
        
        Args:
            model: Model to benchmark
            input_shape: Input tensor shape (batch_size, seq_len, features)
            num_runs: Number of benchmark runs
            
        Returns:
            Benchmark results
        """
        model.eval()
        dummy_input = torch.randn(*input_shape).to(self.device)
        
        # Warm up
        with torch.no_grad():
            for _ in range(10):
                _ = model(dummy_input)
        
        # Benchmark
        if self.device.type == 'cuda':
            torch.cuda.synchronize()
        
        import time
        start_time = time.time()
        
        with torch.no_grad():
            for _ in range(num_runs):
                _ = model(dummy_input)
        
        if self.device.type == 'cuda':
            torch.cuda.synchronize()
        
        end_time = time.time()
        
        total_time = end_time - start_time
        avg_time = total_time / num_runs
        throughput = num_runs / total_time
        
        return {
            'total_time_s': total_time,
            'avg_time_ms': avg_time * 1000,
            'throughput_fps': throughput,
            'device': str(self.device)
        }


def get_gpu_accelerator() -> GPUAccelerator:
    """
    Factory function to get GPU accelerator instance.
    
    Returns:
        GPUAccelerator instance
    """
    return GPUAccelerator()


def safe_cuda_empty_cache():
    """Safely clear CUDA cache if available."""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        gc.collect()


def get_gpu_memory_info() -> Dict[str, Any]:
    """Get GPU memory information."""
    if torch.cuda.is_available():
        return {
            'allocated_gb': torch.cuda.memory_allocated() / 1e9,
            'cached_gb': torch.cuda.memory_reserved() / 1e9,
            'total_gb': torch.cuda.get_device_properties(0).total_memory / 1e9,
            'device_count': torch.cuda.device_count(),
            'current_device': torch.cuda.current_device()
        }
    else:
        return {'message': 'CUDA not available'}


# Export public interface
__all__ = [
    'GPUAccelerator',
    'get_gpu_accelerator',
    'safe_cuda_empty_cache',
    'get_gpu_memory_info'
]
