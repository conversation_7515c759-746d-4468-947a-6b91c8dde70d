"""
Machine Learning Core Package

This package contains all core machine learning functionality:
- algorithms/: Core ML algorithms (regression, metrics, preprocessing)
- models/: Model implementations and interfaces
- optimization/: Performance optimization (GPU, memory, performance monitoring)
- integration/: Advanced ML integrations and enhancements

This provides a clean, organized interface to all ML functionality in the system.
"""

# Import core ML components with safe fallbacks
try:
    from .algorithms.regression import MLRModelWrapper, create_mlr_model, validate_mlr_assumptions
    from .algorithms.metrics import cal_r2, cal_cc, evaluate_model_comprehensive
    ALGORITHMS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ ML algorithms not available: {e}")
    MLRModelWrapper = None
    create_mlr_model = None
    validate_mlr_assumptions = None
    cal_r2 = None
    cal_cc = None
    evaluate_model_comprehensive = None
    ALGORITHMS_AVAILABLE = False

try:
    from .optimization.gpu import GPUAccelerator, safe_cuda_empty_cache
    OPTIMIZATION_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ ML optimization not available: {e}")
    GPUAccelerator = None
    safe_cuda_empty_cache = None
    OPTIMIZATION_AVAILABLE = False

try:
    from .models.shallow import get_shallow_models, create_shallow_model
    from .models.deep import get_deep_models, create_deep_model
    MODELS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ ML models not available: {e}")
    get_shallow_models = None
    create_shallow_model = None
    get_deep_models = None
    create_deep_model = None
    MODELS_AVAILABLE = False

try:
    from .integration.phase1 import (
        impute_logs_deep_phase1,
        impute_logs_deep_phase1_optimized,
        impute_logs_deep_phase1_safe
    )
    INTEGRATION_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ ML integration not available: {e}")
    impute_logs_deep_phase1 = None
    impute_logs_deep_phase1_optimized = None
    impute_logs_deep_phase1_safe = None
    INTEGRATION_AVAILABLE = False

# Export public interface
__all__ = [
    # Algorithms
    'MLRModelWrapper',
    'create_mlr_model',
    'validate_mlr_assumptions',
    'cal_r2',
    'cal_cc',
    'evaluate_model_comprehensive',
    
    # Optimization
    'GPUAccelerator',
    'safe_cuda_empty_cache',
    
    # Models
    'get_shallow_models',
    'create_shallow_model',
    'get_deep_models',
    'create_deep_model',
    
    # Integration
    'impute_logs_deep_phase1',
    'impute_logs_deep_phase1_optimized',
    'impute_logs_deep_phase1_safe',
    
    # Status flags
    'ALGORITHMS_AVAILABLE',
    'OPTIMIZATION_AVAILABLE',
    'MODELS_AVAILABLE',
    'INTEGRATION_AVAILABLE'
]

print("ML core package initialized")
