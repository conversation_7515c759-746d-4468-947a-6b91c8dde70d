#!/usr/bin/env python3
"""
Test Script for Core Code Structure

This script tests that the new core_code package structure works correctly
and that backward compatibility is maintained.
"""

import sys
import traceback
from typing import Dict, Any


def test_core_code_imports() -> Dict[str, Any]:
    """Test importing from the new core_code structure."""
    results = {
        'ml_algorithms': False,
        'ml_optimization': False,
        'data_processing': False,
        'errors': []
    }
    
    # Test ML algorithms
    try:
        from core_code.ml.algorithms.regression import MLR<PERSON>odelWrapper, create_mlr_model
        from core_code.ml.algorithms.metrics import cal_r2, cal_cc, evaluate_model_comprehensive
        print("✅ ML algorithms imported successfully from core_code")
        results['ml_algorithms'] = True
    except ImportError as e:
        error_msg = f"❌ ML algorithms import failed: {e}"
        print(error_msg)
        results['errors'].append(error_msg)
    
    # Test ML optimization
    try:
        from core_code.ml.optimization.gpu import GPUAccelerator, safe_cuda_empty_cache
        print("✅ ML optimization imported successfully from core_code")
        results['ml_optimization'] = True
    except ImportError as e:
        error_msg = f"❌ ML optimization import failed: {e}"
        print(error_msg)
        results['errors'].append(error_msg)
    
    # Test data processing (structure only)
    try:
        from core_code.data import LOADERS_AVAILABLE, PREPROCESSING_AVAILABLE
        print("✅ Data processing package structure imported successfully")
        results['data_processing'] = True
    except ImportError as e:
        error_msg = f"❌ Data processing import failed: {e}"
        print(error_msg)
        results['errors'].append(error_msg)
    
    return results


def test_backward_compatibility() -> Dict[str, Any]:
    """Test that backward compatibility is maintained."""
    results = {
        'ml_core_imports': False,
        'deprecated_warnings': False,
        'errors': []
    }
    
    # Test that ml_core can import from new locations
    try:
        from utils.ml_core import MODEL_REGISTRY, MLR_AVAILABLE, GPU_UTILS_AVAILABLE
        print("✅ ml_core imports working with new structure")
        results['ml_core_imports'] = True
    except ImportError as e:
        error_msg = f"❌ ml_core import failed: {e}"
        print(error_msg)
        results['errors'].append(error_msg)
    
    # Test deprecated imports (should work but show warnings)
    try:
        import warnings
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            from utils.metrics import cal_r2
            from utils.optimization import GPUAccelerator
            
            if len(w) > 0:
                print("✅ Deprecation warnings working correctly")
                results['deprecated_warnings'] = True
            else:
                print("⚠️ No deprecation warnings detected")
                
    except ImportError as e:
        error_msg = f"❌ Backward compatibility test failed: {e}"
        print(error_msg)
        results['errors'].append(error_msg)
    
    return results


def test_functionality() -> Dict[str, Any]:
    """Test that the functionality actually works."""
    results = {
        'mlr_model': False,
        'gpu_accelerator': False,
        'metrics': False,
        'errors': []
    }
    
    # Test MLR model creation
    try:
        from core_code.ml.algorithms.regression import create_mlr_model
        model = create_mlr_model(model_type='linear')
        print("✅ MLR model creation working")
        results['mlr_model'] = True
    except Exception as e:
        error_msg = f"❌ MLR model test failed: {e}"
        print(error_msg)
        results['errors'].append(error_msg)
    
    # Test GPU accelerator
    try:
        from core_code.ml.optimization.gpu import GPUAccelerator
        gpu = GPUAccelerator()
        device_info = gpu.get_device_info()
        print(f"✅ GPU accelerator working: {device_info.get('name', 'Unknown')}")
        results['gpu_accelerator'] = True
    except Exception as e:
        error_msg = f"❌ GPU accelerator test failed: {e}"
        print(error_msg)
        results['errors'].append(error_msg)
    
    # Test metrics
    try:
        from core_code.ml.algorithms.metrics import calculate_prediction_metrics
        import numpy as np
        
        y_true = np.array([1, 2, 3, 4, 5])
        y_pred = np.array([1.1, 2.1, 2.9, 4.1, 4.9])
        metrics = calculate_prediction_metrics(y_true, y_pred)
        print(f"✅ Metrics calculation working: R² = {metrics['r2']:.3f}")
        results['metrics'] = True
    except Exception as e:
        error_msg = f"❌ Metrics test failed: {e}"
        print(error_msg)
        results['errors'].append(error_msg)
    
    return results


def main():
    """Run all tests and report results."""
    print("🧪 Testing Core Code Structure")
    print("=" * 50)
    
    # Test imports
    print("\n📦 Testing Core Code Imports")
    print("-" * 30)
    import_results = test_core_code_imports()
    
    # Test backward compatibility
    print("\n🔄 Testing Backward Compatibility")
    print("-" * 30)
    compat_results = test_backward_compatibility()
    
    # Test functionality
    print("\n⚙️ Testing Functionality")
    print("-" * 30)
    func_results = test_functionality()
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 50)
    
    total_tests = 0
    passed_tests = 0
    
    for category, results in [
        ("Core Code Imports", import_results),
        ("Backward Compatibility", compat_results),
        ("Functionality", func_results)
    ]:
        print(f"\n{category}:")
        for test_name, passed in results.items():
            if test_name != 'errors':
                total_tests += 1
                if passed:
                    passed_tests += 1
                    print(f"  ✅ {test_name}")
                else:
                    print(f"  ❌ {test_name}")
        
        if results['errors']:
            print(f"  Errors: {len(results['errors'])}")
            for error in results['errors']:
                print(f"    - {error}")
    
    print(f"\n🎯 Overall Result: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! Core code structure is working correctly.")
        return 0
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
        return 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"💥 Test script failed with error: {e}")
        traceback.print_exc()
        sys.exit(1)
