"""
ML Performance Optimization Package

This package consolidates all performance optimization utilities for machine learning:
- gpu_utils.py: GPU acceleration and CUDA utilities
- memory.py: Memory optimization and management
- performance.py: Performance monitoring and profiling

These utilities are consolidated from various optimization modules to provide
a centralized performance optimization library.
"""

# Import optimization utilities with safe fallbacks
try:
    from .gpu_utils import GPUAccelerator
    GPU_UTILS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ GPU utilities not available: {e}")
    GPUAccelerator = None
    GPU_UTILS_AVAILABLE = False

try:
    from .memory import MemoryOptimizer, get_memory_optimizer
    MEMORY_UTILS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Memory optimization utilities not available: {e}")
    MemoryOptimizer = None
    get_memory_optimizer = None
    MEMORY_UTILS_AVAILABLE = False

try:
    from .performance import PerformanceMonitor, profile_function
    PERFORMANCE_UTILS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Performance monitoring utilities not available: {e}")
    PerformanceMonitor = None
    profile_function = None
    PERFORMANCE_UTILS_AVAILABLE = False

# Export public interface
__all__ = [
    # GPU utilities
    'GPUAccelerator',
    
    # Memory optimization
    'MemoryOptimizer',
    'get_memory_optimizer',
    
    # Performance monitoring
    'PerformanceMonitor',
    'profile_function',
    
    # Status flags
    'GPU_UTILS_AVAILABLE',
    'MEMORY_UTILS_AVAILABLE',
    'PERFORMANCE_UTILS_AVAILABLE'
]

print("ML optimization package initialized")
