"""
Performance Optimization Utilities for ML Log Prediction
GPU acceleration, memory optimization, and parallel processing for multi-model workflows

DEPRECATED: This module has been reorganized. Please use:
- utils.ml.optimization.gpu_utils for GPU acceleration
- utils.ml.optimization.memory for memory optimization
- utils.ml.optimization.performance for performance monitoring
"""

import torch
import numpy as np
import psutil
import gc
from typing import Dict, List, Any, Optional, Callable, Tuple
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing as mp
from memory_profiler import profile
import warnings

# Issue deprecation warning
warnings.warn(
    "utils.optimization is deprecated. Please use utils.ml.optimization submodules instead.",
    DeprecationWarning,
    stacklevel=2
)

class GPUAccelerator:
    """GPU acceleration utilities for deep learning models."""
    
    def __init__(self):
        """Initialize GPU accelerator."""
        self.device = self._detect_best_device()
        self.memory_fraction = 0.8  # Use 80% of GPU memory
        self._setup_gpu_memory()
        
    def _detect_best_device(self) -> torch.device:
        """Detect the best available device."""
        if torch.cuda.is_available():
            device = torch.device('cuda')
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
            print(f"🚀 GPU detected: {gpu_name} ({gpu_memory:.1f} GB)")
            return device
        else:
            print("💻 Using CPU (GPU not available)")
            return torch.device('cpu')
    
    def _setup_gpu_memory(self) -> None:
        """Setup GPU memory management."""
        if self.device.type == 'cuda':
            # Clear cache
            torch.cuda.empty_cache()
            
            # Set memory fraction
            total_memory = torch.cuda.get_device_properties(0).total_memory
            torch.cuda.set_per_process_memory_fraction(self.memory_fraction)
            
            print(f"🔧 GPU memory configured: {self.memory_fraction*100:.0f}% of {total_memory/1e9:.1f} GB")
    
    def optimize_model_for_gpu(self, model: torch.nn.Module) -> torch.nn.Module:
        """
        Optimize model for GPU execution.
        
        Args:
            model: PyTorch model to optimize
            
        Returns:
            Optimized model
        """
        if self.device.type == 'cuda':
            model = model.to(self.device)
            
            # Enable mixed precision if supported
            if hasattr(torch.cuda, 'amp'):
                print("⚡ Mixed precision training enabled")
                model = torch.jit.script(model) if hasattr(torch, 'jit') else model
            
            # Optimize for inference if possible
            model.eval()
            with torch.no_grad():
                # Warm up GPU
                dummy_input = torch.randn(1, 64, 4).to(self.device)
                try:
                    _ = model(dummy_input)
                    print("🔥 GPU warm-up completed")
                except Exception as e:
                    print(f"⚠️ GPU warm-up failed: {e}")
        
        return model
    
    def get_memory_stats(self) -> Dict[str, float]:
        """Get current GPU memory statistics."""
        if self.device.type == 'cuda':
            allocated = torch.cuda.memory_allocated() / 1e9
            cached = torch.cuda.memory_reserved() / 1e9
            total = torch.cuda.get_device_properties(0).total_memory / 1e9
            
            return {
                'allocated_gb': allocated,
                'cached_gb': cached,
                'total_gb': total,
                'utilization_percent': (allocated / total) * 100
            }
        else:
            return {'message': 'GPU not available'}
    
    def clear_memory(self) -> None:
        """Clear GPU memory cache."""
        if self.device.type == 'cuda':
            torch.cuda.empty_cache()
            gc.collect()
            print("🧹 GPU memory cache cleared")

class MemoryOptimizer:
    """Memory optimization utilities for large datasets and models."""
    
    def __init__(self):
        """Initialize memory optimizer."""
        self.system_memory_gb = psutil.virtual_memory().total / 1e9
        self.available_memory_gb = psutil.virtual_memory().available / 1e9
        print(f"💾 System memory: {self.system_memory_gb:.1f} GB (Available: {self.available_memory_gb:.1f} GB)")
    
    def optimize_batch_size(self, model_size_mb: float, sequence_length: int, 
                           n_features: int, safety_factor: float = 0.7) -> int:
        """
        Calculate optimal batch size based on available memory.
        
        Args:
            model_size_mb: Model size in MB
            sequence_length: Input sequence length
            n_features: Number of features
            safety_factor: Safety factor for memory usage
            
        Returns:
            Optimal batch size
        """
        # Estimate memory per sample (in MB)
        sample_memory_mb = (sequence_length * n_features * 4) / 1e6  # 4 bytes per float32
        
        # Available memory for batch processing
        available_memory_mb = self.available_memory_gb * 1000 * safety_factor
        
        # Reserve memory for model and overhead
        usable_memory_mb = available_memory_mb - model_size_mb - 500  # 500MB overhead
        
        if usable_memory_mb <= 0:
            print("⚠️ Warning: Very limited memory available")
            return 1
        
        # Calculate optimal batch size
        optimal_batch_size = int(usable_memory_mb / (sample_memory_mb * 10))  # 10x for gradients/activations
        optimal_batch_size = max(1, min(optimal_batch_size, 128))  # Clamp between 1 and 128
        
        print(f"🎯 Optimal batch size: {optimal_batch_size} (Memory per sample: {sample_memory_mb:.2f} MB)")
        return optimal_batch_size
    
    def enable_gradient_checkpointing(self, model: torch.nn.Module) -> torch.nn.Module:
        """
        Enable gradient checkpointing to reduce memory usage.
        
        Args:
            model: PyTorch model
            
        Returns:
            Model with gradient checkpointing enabled
        """
        if hasattr(model, 'gradient_checkpointing_enable'):
            model.gradient_checkpointing_enable()
            print("✅ Gradient checkpointing enabled")
        else:
            print("⚠️ Gradient checkpointing not supported by this model")
        
        return model
    
    def monitor_memory_usage(self) -> Dict[str, float]:
        """Monitor current memory usage."""
        memory = psutil.virtual_memory()
        
        return {
            'total_gb': memory.total / 1e9,
            'available_gb': memory.available / 1e9,
            'used_gb': memory.used / 1e9,
            'percent_used': memory.percent,
            'free_gb': memory.free / 1e9
        }
    
    def optimize_data_loading(self, data: np.ndarray, chunk_size: Optional[int] = None) -> List[np.ndarray]:
        """
        Optimize data loading by chunking large datasets.
        
        Args:
            data: Input data array
            chunk_size: Optional chunk size (auto-calculated if None)
            
        Returns:
            List of data chunks
        """
        if chunk_size is None:
            # Calculate chunk size based on available memory
            data_size_mb = data.nbytes / 1e6
            available_memory_mb = self.available_memory_gb * 1000 * 0.5  # Use 50% of available memory
            
            if data_size_mb <= available_memory_mb:
                return [data]  # No chunking needed
            
            chunk_size = int(len(data) * (available_memory_mb / data_size_mb))
            chunk_size = max(1, chunk_size)
        
        chunks = []
        for i in range(0, len(data), chunk_size):
            chunks.append(data[i:i + chunk_size])
        
        print(f"📦 Data chunked into {len(chunks)} pieces (chunk size: {chunk_size})")
        return chunks

class ParallelProcessor:
    """Parallel processing utilities for multi-model workflows."""
    
    def __init__(self, max_workers: Optional[int] = None):
        """
        Initialize parallel processor.
        
        Args:
            max_workers: Maximum number of worker processes/threads
        """
        self.cpu_count = mp.cpu_count()
        self.max_workers = max_workers or min(self.cpu_count, 8)  # Limit to 8 workers
        print(f"🔄 Parallel processor initialized: {self.max_workers} workers (CPU cores: {self.cpu_count})")
    
    def run_models_parallel(self, model_configs: List[Dict[str, Any]], 
                           train_data: np.ndarray, truth_data: np.ndarray,
                           use_processes: bool = False) -> List[Any]:
        """
        Run multiple models in parallel.
        
        Args:
            model_configs: List of model configurations
            train_data: Training data
            truth_data: Ground truth data
            use_processes: Whether to use processes (True) or threads (False)
            
        Returns:
            List of trained models
        """
        executor_class = ProcessPoolExecutor if use_processes else ThreadPoolExecutor
        
        print(f"🚀 Running {len(model_configs)} models in parallel using {'processes' if use_processes else 'threads'}")
        
        with executor_class(max_workers=self.max_workers) as executor:
            futures = []
            
            for config in model_configs:
                future = executor.submit(self._train_single_model, config, train_data, truth_data)
                futures.append(future)
            
            results = []
            for i, future in enumerate(futures):
                try:
                    result = future.result(timeout=3600)  # 1 hour timeout
                    results.append(result)
                    print(f"✅ Model {i+1}/{len(model_configs)} completed")
                except Exception as e:
                    print(f"❌ Model {i+1}/{len(model_configs)} failed: {e}")
                    results.append(None)
        
        return results
    
    def _train_single_model(self, config: Dict[str, Any], 
                           train_data: np.ndarray, truth_data: np.ndarray) -> Any:
        """Train a single model (helper function for parallel execution)."""
        try:
            model_class = config['model_class']
            hyperparams = config['hyperparams']
            
            # Initialize model
            model = model_class(**hyperparams)
            
            # Convert data to tensors
            train_tensor = torch.tensor(train_data, dtype=torch.float32)
            truth_tensor = torch.tensor(truth_data, dtype=torch.float32)
            
            # Train model
            model.fit(train_tensor, truth_tensor)
            
            return model
            
        except Exception as e:
            print(f"❌ Model training failed: {e}")
            raise
    
    def batch_process_data(self, data_chunks: List[np.ndarray], 
                          processing_func: Callable, **kwargs) -> List[Any]:
        """
        Process data chunks in parallel.
        
        Args:
            data_chunks: List of data chunks to process
            processing_func: Function to apply to each chunk
            **kwargs: Additional arguments for processing function
            
        Returns:
            List of processed results
        """
        print(f"🔄 Processing {len(data_chunks)} data chunks in parallel")
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = [executor.submit(processing_func, chunk, **kwargs) for chunk in data_chunks]
            
            results = []
            for i, future in enumerate(futures):
                try:
                    result = future.result()
                    results.append(result)
                    print(f"✅ Chunk {i+1}/{len(data_chunks)} processed")
                except Exception as e:
                    print(f"❌ Chunk {i+1}/{len(data_chunks)} failed: {e}")
                    results.append(None)
        
        return results

class PerformanceMonitor:
    """Performance monitoring utilities."""
    
    def __init__(self):
        """Initialize performance monitor."""
        self.gpu_accelerator = GPUAccelerator()
        self.memory_optimizer = MemoryOptimizer()
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        status = {
            'cpu_percent': psutil.cpu_percent(interval=1),
            'cpu_count': psutil.cpu_count(),
            'memory': self.memory_optimizer.monitor_memory_usage(),
            'gpu': self.gpu_accelerator.get_memory_stats()
        }
        
        return status
    
    def print_system_status(self) -> None:
        """Print formatted system status."""
        status = self.get_system_status()
        
        print("📊 System Performance Status:")
        print(f"   CPU Usage: {status['cpu_percent']:.1f}% ({status['cpu_count']} cores)")
        print(f"   Memory: {status['memory']['used_gb']:.1f}/{status['memory']['total_gb']:.1f} GB ({status['memory']['percent_used']:.1f}%)")
        
        if isinstance(status['gpu'], dict) and 'allocated_gb' in status['gpu']:
            print(f"   GPU Memory: {status['gpu']['allocated_gb']:.1f}/{status['gpu']['total_gb']:.1f} GB ({status['gpu']['utilization_percent']:.1f}%)")
        else:
            print("   GPU: Not available")
    
    def optimize_for_model(self, model: torch.nn.Module, 
                          data_shape: Tuple[int, ...]) -> Dict[str, Any]:
        """
        Optimize system for a specific model.
        
        Args:
            model: PyTorch model
            data_shape: Shape of input data (batch_size, seq_len, features)
            
        Returns:
            Optimization recommendations
        """
        # Estimate model size
        model_params = sum(p.numel() for p in model.parameters())
        model_size_mb = model_params * 4 / 1e6  # 4 bytes per float32
        
        # Calculate optimal batch size
        batch_size, seq_len, n_features = data_shape
        optimal_batch_size = self.memory_optimizer.optimize_batch_size(
            model_size_mb, seq_len, n_features
        )
        
        # GPU optimization
        optimized_model = self.gpu_accelerator.optimize_model_for_gpu(model)
        
        recommendations = {
            'model_size_mb': model_size_mb,
            'model_parameters': model_params,
            'optimal_batch_size': optimal_batch_size,
            'device': str(self.gpu_accelerator.device),
            'memory_optimized': True,
            'gpu_optimized': self.gpu_accelerator.device.type == 'cuda'
        }
        
        print("🎯 Optimization Recommendations:")
        for key, value in recommendations.items():
            print(f"   {key}: {value}")
        
        return recommendations
