"""
Data Processing Core Package

This package contains all core data processing functionality:
- loaders.py: LAS file loading and data ingestion
- preprocessing.py: Data preprocessing and normalization
- validation.py: Data validation and quality checks

These components handle all aspects of data processing for the ML pipeline.
"""

# Import data processing components with safe fallbacks
try:
    from .loaders import (
        load_las_files_from_directory,
        load_las_files,
        clean_log_data,
        write_results_to_las
    )
    LOADERS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Data loaders not available: {e}")
    load_las_files_from_directory = None
    load_las_files = None
    clean_log_data = None
    write_results_to_las = None
    LOADERS_AVAILABLE = False

try:
    from .preprocessing import (
        normalize_data,
        create_sequences,
        introduce_missingness,
        prepare_prediction_data
    )
    PREPROCESSING_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Data preprocessing not available: {e}")
    normalize_data = None
    create_sequences = None
    introduce_missingness = None
    prepare_prediction_data = None
    PREPROCESSING_AVAILABLE = False

try:
    from .validation import (
        validate_data_quality,
        check_data_leakage,
        validate_feature_distributions
    )
    VALIDATION_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Data validation not available: {e}")
    validate_data_quality = None
    check_data_leakage = None
    validate_feature_distributions = None
    VALIDATION_AVAILABLE = False

# Export public interface
__all__ = [
    # Loaders
    'load_las_files_from_directory',
    'load_las_files',
    'clean_log_data',
    'write_results_to_las',
    
    # Preprocessing
    'normalize_data',
    'create_sequences',
    'introduce_missingness',
    'prepare_prediction_data',
    
    # Validation
    'validate_data_quality',
    'check_data_leakage',
    'validate_feature_distributions',
    
    # Status flags
    'LOADERS_AVAILABLE',
    'PREPROCESSING_AVAILABLE',
    'VALIDATION_AVAILABLE'
]

print("Data processing package initialized")
