# ML Utilities Migration Guide

This guide helps you migrate from the old scattered ML utilities to the new consolidated `utils/ml/` package structure.

## New Package Structure

```
utils/ml/
├── __init__.py                  # Main ML utilities package
├── algorithms/                  # Core ML algorithms
│   ├── __init__.py
│   ├── regression.py           # MLR utilities (from mlr_utils.py)
│   ├── metrics.py              # Evaluation metrics (from metrics.py)
│   └── preprocessing.py        # Advanced preprocessing (from stability_core.py)
├── optimization/               # Performance optimization
│   ├── __init__.py
│   ├── gpu_utils.py           # GPU acceleration (from optimization.py)
│   ├── memory.py              # Memory optimization
│   └── performance.py         # Performance monitoring
└── integration/               # Advanced integrations
    ├── __init__.py
    └── phase1_core.py         # Phase 1 integration (from preprocessing/)
```

## Migration Examples

### Before (Old Imports)
```python
# Old scattered imports
from utils.metrics import cal_r2, cal_cc
from utils.optimization import GPUAccelerator
from utils.mlr_utils import MLRModelWrapper, create_mlr_model
from preprocessing.deep_model.ml_core_phase1_integration import impute_logs_deep_phase1_safe
```

### After (New Consolidated Imports)
```python
# New consolidated imports
from utils.ml.algorithms.metrics import cal_r2, cal_cc
from utils.ml.optimization.gpu_utils import GPUAccelerator
from utils.ml.algorithms.regression import MLRModelWrapper, create_mlr_model
from utils.ml.integration.phase1_core import impute_logs_deep_phase1_safe

# Or use package-level imports
from utils.ml.algorithms import cal_r2, cal_cc, MLRModelWrapper
from utils.ml.optimization import GPUAccelerator
from utils.ml.integration import impute_logs_deep_phase1_safe
```

## Backward Compatibility

The old import paths are still supported with deprecation warnings:

```python
# These still work but will show deprecation warnings
from utils.metrics import cal_r2  # ⚠️ Deprecated
from utils.optimization import GPUAccelerator  # ⚠️ Deprecated
```

## Benefits of New Structure

1. **Better Organization**: Related functionality is grouped together
2. **Clearer Dependencies**: Easy to see what components depend on what
3. **Easier Maintenance**: Centralized location for each type of utility
4. **Better Documentation**: Each package has clear purpose and interface
5. **Future Extensibility**: Easy to add new algorithms and optimizations

## Migration Timeline

- **Phase 1**: New structure created with backward compatibility
- **Phase 2**: Update all internal imports to use new structure
- **Phase 3**: Remove deprecated import paths (future release)

## Key Changes Summary

| Old Location | New Location | Status |
|-------------|-------------|---------|
| `utils/metrics.py` | `utils/ml/algorithms/metrics.py` | ✅ Migrated |
| `utils/optimization.py` | `utils/ml/optimization/gpu_utils.py` | ✅ Migrated |
| `utils/mlr_utils.py` | `utils/ml/algorithms/regression.py` | 📋 Planned |
| `utils/stability_core.py` | `utils/ml/algorithms/preprocessing.py` | 📋 Planned |
| `preprocessing/deep_model/ml_core_phase1_integration.py` | `utils/ml/integration/phase1_core.py` | 📋 Planned |

## Next Steps

1. Update your imports to use the new consolidated structure
2. Test that functionality works as expected
3. Remove any direct dependencies on deprecated modules
4. Enjoy the improved organization! 🎉
