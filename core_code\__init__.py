"""
Core Code Package for ML Log Prediction System

This package contains all core machine learning functionality, data processing,
and utilities organized in a clean, maintainable structure.

Structure:
- ml/: Machine learning algorithms, models, and optimization
- data/: Data loading, preprocessing, and validation
- utils/: Core utilities and configuration management

This replaces the scattered utilities in the utils/ directory with a more
organized and professional structure.
"""

# Import key components for easy access
try:
    from .ml import MLRModelWrapper, GPUAccelerator, cal_r2, cal_cc
    from .data import load_las_files, normalize_data, create_sequences
    ML_CORE_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Some core components not available: {e}")
    MLRModelWrapper = None
    GPUAccelerator = None
    cal_r2 = None
    cal_cc = None
    load_las_files = None
    normalize_data = None
    create_sequences = None
    ML_CORE_AVAILABLE = False

# Version and metadata
__version__ = "1.0.0"
__author__ = "ML Log Prediction System"
__description__ = "Core machine learning and data processing functionality"

# Export public interface
__all__ = [
    # ML components
    'MLRModelWrapper',
    'GPUAccelerator',
    'cal_r2',
    'cal_cc',
    
    # Data components
    'load_las_files',
    'normalize_data',
    'create_sequences',
    
    # Status
    'ML_CORE_AVAILABLE'
]

print("Core code package initialized")
