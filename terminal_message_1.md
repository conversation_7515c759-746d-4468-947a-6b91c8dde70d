[PHASE1] Configuring memory optimization environment...
   [OK] PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\15_ML_Log_Prediction\branch_3_gpu_rk1\main.py:15: DeprecationWarning: utils.data_handler is deprecated. Please use core_code.data submodules instead.
  from utils.data_handler import load_las_files_from_directory, clean_log_data, write_results_to_las     
Enhanced preprocessing not available. Using standard preprocessing.
ML algorithms package initialized
ML optimization package initialized
c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\15_ML_Log_Prediction\branch_3_gpu_rk1\core_code\ml\models\deep.py:34: UserWarning: TensorFlow not available.
  warnings.warn("TensorFlow not available.")
Loading advanced deep learning models...
WARNING: Failed to find MSVC.
WARNING: Failed to find Windows SDK.
WARNING: Failed to find CUDA.
c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\15_ML_Log_Prediction\branch_3_gpu_rk1\models\advanced_models\saits_model.py:21: UserWarning: PyPOTS not available: Traceback (most recent call last):
  File "C:\Users\<USER>\mwlt\lib\site-packages\tensorflow\python\pywrap_tensorflow.py", line 73, in <module>
    from tensorflow.python._pywrap_tensorflow_internal import *
ImportError: DLL load failed while importing _pywrap_tensorflow_internal: A dynamic link library (DLL) initialization routine failed.


Failed to load the native TensorFlow runtime.
See https://www.tensorflow.org/install/errors for some common causes and solutions.
If you need help, create an issue at https://github.com/tensorflow/tensorflow/issues and include the entire stack trace above this error message.
  warnings.warn(f"PyPOTS not available: {e}")
SAITS model loaded successfully
c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\15_ML_Log_Prediction\branch_3_gpu_rk1\models\advanced_models\brits_model.py:21: UserWarning: PyPOTS not available: Traceback (most recent call last):
  File "C:\Users\<USER>\mwlt\lib\site-packages\tensorflow\python\pywrap_tensorflow.py", line 73, in <module>
    from tensorflow.python._pywrap_tensorflow_internal import *
ImportError: DLL load failed while importing _pywrap_tensorflow_internal: A dynamic link library (DLL) initialization routine failed.


Failed to load the native TensorFlow runtime.
See https://www.tensorflow.org/install/errors for some common causes and solutions.
If you need help, create an issue at https://github.com/tensorflow/tensorflow/issues and include the entire stack trace above this error message.
  warnings.warn(f"PyPOTS not available: {e}")
BRITS model loaded successfully
Advanced models loaded: ['saits', 'brits']
Total available: 2/5 models
Advanced models module initialized (Phase 1 foundation)
Ready for Phase 2: Core model implementations
ML models package initialized
Data processing package initialized
c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\2_Pyth_Script\15_ML_Log_Prediction\branch_3_gpu_rk1\core_code\ml\integration\phase1.py:75: UserWarning: Original ML core not available  
  warnings.warn("Original ML core not available")
Phase 1 ML integration loaded successfully
ML integration package initialized with Phase 1 enhancements
ML core package initialized
Core code package initialized
Multiple Linear Regression utilities loaded successfully (from core_code)
Data leakage detection module loaded
GPU utilities loaded successfully (from core_code)
Advanced deep learning models module loaded
Available advanced models: ['saits', 'brits']
SAITS model added to registry
BRITS model added to registry
Enhanced MODEL_REGISTRY with 2 advanced models
Available advanced models: ['saits', 'brits']
   [OK] Phase 1 Enhanced Deep Learning Integration loaded
INFO:utils.display_utils:Configuring fonts for Windows system
INFO:utils.display_utils:Set matplotlib font to: Segoe UI Emoji
INFO:utils.display_utils:Emoji support confirmed
INFO:utils.display_utils:Configured warning filters for font issues
🔍 Performance Monitor initialized
   • GPU monitoring enabled
   • Monitoring interval: 1.0s
[MEM] Environment configured for memory optimization
[MEM] Memory Optimizer initialized
   * Mixed precision enabled
   * Memory monitoring enabled
   [OK] Memory optimizer initialized
============================================================
 ML LOG PREDICTION
============================================================

[MEM] Initial Memory Status:

==================================================
[MEM] MEMORY STATUS
==================================================
System Memory:
   * Total: 31.7 GB
   * Available: 6.8 GB
   * Usage: 78.6%

GPU Memory:
   * Total: 4.0 GB
   * Allocated: 0.0 GB
   * Reserved: 0.0 GB
   * Free: 4.0 GB
==================================================
