"""
Advanced Data Preprocessing & Training Stabilization

This module implements comprehensive data preprocessing and stabilization strategies
for deep learning models. Moved from utils/stability_core.py for better organization.

Key Features:
- Advanced Input Validation & Cleaning
- Enhanced Normalization Pipeline  
- Missing Value Encoding
- Numerical Stability Checks
- Well Log Range Validation

Author: Advanced Preprocessing Pipeline
"""

import numpy as np
import pandas as pd
import warnings
import logging
from typing import Dict, List, Tuple, Optional, Union, Any
from sklearn.preprocessing import StandardScaler, RobustScaler, QuantileTransformer
from sklearn.impute import SimpleImputer
import torch

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Well log type ranges for validation (based on common industry standards)
WELL_LOG_RANGES = {
    'GR': (0, 300),      # Gamma Ray (API units)
    'NPHI': (0, 1),      # Neutron Porosity (fraction)
    'RHOB': (1.5, 3.0),  # Bulk Density (g/cm3)
    'DT': (40, 200),     # Delta Time (us/ft)
    'RT': (0.1, 1000),   # Resistivity (ohm-m)
    'SP': (-200, 200),   # Spontaneous Potential (mV)
    'CALI': (6, 20),     # Caliper (inches)
    'PEF': (0, 10),      # Photoelectric Factor
    'MD': (0, 10000),    # Measured Depth (ft/m)
    'TVD': (0, 10000),   # True Vertical Depth (ft/m)
}


def validate_and_clean_input(sequences: np.ndarray, 
                           feature_names: List[str],
                           max_value_threshold: float = 1e10,
                           min_value_threshold: float = -1e10,
                           validate_ranges: bool = True) -> Tuple[np.ndarray, Dict[str, Any]]:
    """
    Comprehensive input validation and cleaning before model training.
    
    Args:
        sequences: Input sequences (n_sequences, seq_len, n_features)
        feature_names: List of feature names for validation
        max_value_threshold: Maximum allowed value
        min_value_threshold: Minimum allowed value
        validate_ranges: Whether to validate well log ranges
        
    Returns:
        Tuple of (cleaned_sequences, validation_report)
    """
    logger.info("🔍 Starting comprehensive input validation and cleaning...")
    
    if not isinstance(sequences, np.ndarray):
        sequences = np.array(sequences, dtype=np.float32)
    
    original_shape = sequences.shape
    n_sequences, seq_len, n_features = original_shape
    
    # Initialize validation report
    validation_report = {
        'original_shape': original_shape,
        'feature_names': feature_names,
        'issues_found': [],
        'corrections_applied': [],
        'data_quality_score': 0.0,
        'feature_statistics': {}
    }
    
    # Create a copy for cleaning
    cleaned_sequences = sequences.copy()
    total_elements = np.prod(original_shape)
    
    # 1. Check for non-finite values (inf, -inf, NaN)
    logger.info("   Checking for non-finite values...")
    inf_mask = np.isinf(sequences)
    inf_count = np.sum(inf_mask)
    
    if inf_count > 0:
        validation_report['issues_found'].append(f"Found {inf_count} infinite values")
        cleaned_sequences[inf_mask] = np.nan
        validation_report['corrections_applied'].append(f"Converted {inf_count} infinite values to NaN")
        logger.warning(f"   Found and converted {inf_count} infinite values to NaN")
    
    # 2. Check for extremely large values
    logger.info("   Checking for extremely large values...")
    large_mask = (np.abs(sequences) > max_value_threshold) & ~np.isnan(sequences)
    large_count = np.sum(large_mask)
    
    if large_count > 0:
        validation_report['issues_found'].append(f"Found {large_count} extremely large values")
        cleaned_sequences[large_mask] = np.nan
        validation_report['corrections_applied'].append(f"Converted {large_count} extremely large values to NaN")
        logger.warning(f"   Found and converted {large_count} extremely large values to NaN")
    
    # 3. Validate well log ranges if requested
    if validate_ranges and len(feature_names) == n_features:
        logger.info("   Validating well log ranges...")
        for i, feature_name in enumerate(feature_names):
            if feature_name.upper() in WELL_LOG_RANGES:
                min_val, max_val = WELL_LOG_RANGES[feature_name.upper()]
                feature_data = cleaned_sequences[:, :, i]
                
                # Check for out-of-range values
                out_of_range_mask = ((feature_data < min_val) | (feature_data > max_val)) & ~np.isnan(feature_data)
                out_of_range_count = np.sum(out_of_range_mask)
                
                if out_of_range_count > 0:
                    validation_report['issues_found'].append(
                        f"Feature {feature_name}: {out_of_range_count} values outside expected range [{min_val}, {max_val}]"
                    )
                    # Optionally clip or set to NaN (here we set to NaN for safety)
                    cleaned_sequences[:, :, i][out_of_range_mask] = np.nan
                    validation_report['corrections_applied'].append(
                        f"Set {out_of_range_count} out-of-range values in {feature_name} to NaN"
                    )
    
    # 4. Calculate data quality score
    nan_count = np.sum(np.isnan(cleaned_sequences))
    data_quality_score = 1.0 - (nan_count / total_elements)
    validation_report['data_quality_score'] = data_quality_score
    
    # 5. Calculate feature statistics
    for i, feature_name in enumerate(feature_names):
        if i < n_features:
            feature_data = cleaned_sequences[:, :, i]
            validation_report['feature_statistics'][feature_name] = {
                'mean': np.nanmean(feature_data),
                'std': np.nanstd(feature_data),
                'min': np.nanmin(feature_data),
                'max': np.nanmax(feature_data),
                'nan_percentage': np.sum(np.isnan(feature_data)) / feature_data.size * 100
            }
    
    logger.info(f"✅ Input validation completed. Data quality score: {data_quality_score:.3f}")
    
    return cleaned_sequences, validation_report


def enhanced_normalize_data(data: np.ndarray, 
                          method: str = 'robust',
                          feature_names: Optional[List[str]] = None,
                          clip_outliers: bool = True,
                          outlier_threshold: float = 5.0) -> Tuple[np.ndarray, Dict[str, Any]]:
    """
    Enhanced data normalization with multiple methods and outlier handling.
    
    Args:
        data: Input data to normalize
        method: Normalization method ('standard', 'robust', 'quantile')
        feature_names: Optional feature names for reporting
        clip_outliers: Whether to clip outliers before normalization
        outlier_threshold: Z-score threshold for outlier detection
        
    Returns:
        Tuple of (normalized_data, normalization_info)
    """
    logger.info(f"🔧 Starting enhanced normalization using {method} method...")
    
    if not isinstance(data, np.ndarray):
        data = np.array(data, dtype=np.float32)
    
    original_shape = data.shape
    
    # Reshape for sklearn if needed
    if len(original_shape) > 2:
        data_reshaped = data.reshape(-1, original_shape[-1])
    else:
        data_reshaped = data
    
    # Handle outliers if requested
    if clip_outliers:
        logger.info("   Clipping outliers...")
        from scipy import stats
        z_scores = np.abs(stats.zscore(data_reshaped, nan_policy='omit'))
        outlier_mask = z_scores > outlier_threshold
        outlier_count = np.sum(outlier_mask)
        
        if outlier_count > 0:
            # Clip outliers to the threshold
            data_reshaped = np.where(outlier_mask, 
                                   np.sign(data_reshaped) * outlier_threshold * np.nanstd(data_reshaped, axis=0) + np.nanmean(data_reshaped, axis=0),
                                   data_reshaped)
            logger.info(f"   Clipped {outlier_count} outliers")
    
    # Choose normalization method
    if method == 'standard':
        scaler = StandardScaler()
    elif method == 'robust':
        scaler = RobustScaler()
    elif method == 'quantile':
        scaler = QuantileTransformer(output_distribution='normal', random_state=42)
    else:
        raise ValueError(f"Unknown normalization method: {method}")
    
    # Fit and transform
    normalized_data = scaler.fit_transform(data_reshaped)
    
    # Reshape back to original shape
    if len(original_shape) > 2:
        normalized_data = normalized_data.reshape(original_shape)
    
    # Create normalization info
    normalization_info = {
        'method': method,
        'original_shape': original_shape,
        'scaler': scaler,
        'outliers_clipped': clip_outliers,
        'feature_statistics': {}
    }
    
    # Calculate statistics if feature names provided
    if feature_names and len(feature_names) == original_shape[-1]:
        for i, feature_name in enumerate(feature_names):
            if len(original_shape) > 2:
                feature_data = normalized_data[:, :, i]
            else:
                feature_data = normalized_data[:, i]
            
            normalization_info['feature_statistics'][feature_name] = {
                'mean': np.nanmean(feature_data),
                'std': np.nanstd(feature_data),
                'min': np.nanmin(feature_data),
                'max': np.nanmax(feature_data)
            }
    
    logger.info("✅ Enhanced normalization completed")
    
    return normalized_data, normalization_info


def numerical_stability_check(data: np.ndarray, 
                            tolerance: float = 1e-7) -> Dict[str, Any]:
    """
    Check numerical stability of data for deep learning models.
    
    Args:
        data: Input data to check
        tolerance: Numerical tolerance for stability checks
        
    Returns:
        Dictionary with stability analysis results
    """
    logger.info("🔍 Performing numerical stability check...")
    
    stability_report = {
        'is_stable': True,
        'issues': [],
        'recommendations': [],
        'statistics': {}
    }
    
    # Check for numerical issues
    if np.any(np.isnan(data)):
        nan_count = np.sum(np.isnan(data))
        stability_report['issues'].append(f"Contains {nan_count} NaN values")
        stability_report['recommendations'].append("Consider imputation or removal of NaN values")
        stability_report['is_stable'] = False
    
    if np.any(np.isinf(data)):
        inf_count = np.sum(np.isinf(data))
        stability_report['issues'].append(f"Contains {inf_count} infinite values")
        stability_report['recommendations'].append("Replace infinite values with finite alternatives")
        stability_report['is_stable'] = False
    
    # Check for extremely small values that might cause underflow
    finite_data = data[np.isfinite(data)]
    if len(finite_data) > 0:
        min_abs_val = np.min(np.abs(finite_data[finite_data != 0]))
        if min_abs_val < tolerance:
            stability_report['issues'].append(f"Contains very small values (min: {min_abs_val:.2e})")
            stability_report['recommendations'].append("Consider scaling or regularization")
    
    # Check for extremely large values
    if len(finite_data) > 0:
        max_abs_val = np.max(np.abs(finite_data))
        if max_abs_val > 1e6:
            stability_report['issues'].append(f"Contains very large values (max: {max_abs_val:.2e})")
            stability_report['recommendations'].append("Consider normalization or clipping")
    
    # Calculate basic statistics
    if len(finite_data) > 0:
        stability_report['statistics'] = {
            'mean': np.mean(finite_data),
            'std': np.std(finite_data),
            'min': np.min(finite_data),
            'max': np.max(finite_data),
            'finite_ratio': len(finite_data) / data.size
        }
    
    logger.info(f"✅ Numerical stability check completed. Stable: {stability_report['is_stable']}")
    
    return stability_report


def phase1_preprocessing_pipeline(sequences: np.ndarray,
                                feature_names: List[str],
                                normalization_method: str = 'robust',
                                validate_ranges: bool = True,
                                clip_outliers: bool = True) -> Tuple[np.ndarray, Dict[str, Any]]:
    """
    Complete Phase 1 preprocessing pipeline combining all stabilization steps.
    
    Args:
        sequences: Input sequences
        feature_names: List of feature names
        normalization_method: Method for normalization
        validate_ranges: Whether to validate well log ranges
        clip_outliers: Whether to clip outliers
        
    Returns:
        Tuple of (processed_sequences, processing_report)
    """
    logger.info("🚀 Starting Phase 1 preprocessing pipeline...")
    
    processing_report = {
        'steps_completed': [],
        'validation_report': {},
        'normalization_info': {},
        'stability_report': {},
        'overall_success': False
    }
    
    try:
        # Step 1: Input validation and cleaning
        cleaned_sequences, validation_report = validate_and_clean_input(
            sequences, feature_names, validate_ranges=validate_ranges
        )
        processing_report['validation_report'] = validation_report
        processing_report['steps_completed'].append('input_validation')
        
        # Step 2: Enhanced normalization
        normalized_sequences, normalization_info = enhanced_normalize_data(
            cleaned_sequences, method=normalization_method, 
            feature_names=feature_names, clip_outliers=clip_outliers
        )
        processing_report['normalization_info'] = normalization_info
        processing_report['steps_completed'].append('normalization')
        
        # Step 3: Final stability check
        stability_report = numerical_stability_check(normalized_sequences)
        processing_report['stability_report'] = stability_report
        processing_report['steps_completed'].append('stability_check')
        
        processing_report['overall_success'] = True
        logger.info("✅ Phase 1 preprocessing pipeline completed successfully")
        
        return normalized_sequences, processing_report
        
    except Exception as e:
        logger.error(f"❌ Phase 1 preprocessing pipeline failed: {e}")
        processing_report['error'] = str(e)
        return sequences, processing_report


def enhanced_validate_sequences(sequences: np.ndarray,
                               feature_names: List[str],
                               sample_rate: float = 1.0) -> Dict[str, Any]:
    """
    Enhanced sequence validation with sampling for performance.
    
    Args:
        sequences: Input sequences to validate
        feature_names: List of feature names
        sample_rate: Fraction of data to sample for validation (0.0 to 1.0)
        
    Returns:
        Validation results dictionary
    """
    logger.info(f"🔍 Enhanced sequence validation (sample rate: {sample_rate:.2f})...")
    
    if sample_rate < 1.0:
        # Sample data for faster validation
        n_samples = int(len(sequences) * sample_rate)
        indices = np.random.choice(len(sequences), n_samples, replace=False)
        sample_sequences = sequences[indices]
    else:
        sample_sequences = sequences
    
    # Perform validation on sample
    _, validation_report = validate_and_clean_input(
        sample_sequences, feature_names, validate_ranges=True
    )
    
    # Scale results back to full dataset if sampling was used
    if sample_rate < 1.0:
        validation_report['note'] = f"Results based on {sample_rate:.2%} sample of data"
        validation_report['estimated_full_dataset'] = True
    
    return validation_report


def get_recommended_preprocessing_config(data_shape: Tuple[int, ...],
                                       feature_names: List[str],
                                       target_model: str = 'deep') -> Dict[str, Any]:
    """
    Get recommended preprocessing configuration based on data characteristics.
    
    Args:
        data_shape: Shape of the input data
        feature_names: List of feature names
        target_model: Target model type ('deep', 'shallow')
        
    Returns:
        Recommended preprocessing configuration
    """
    n_sequences, seq_len, n_features = data_shape
    
    config = {
        'normalization_method': 'robust',  # Default to robust for well log data
        'validate_ranges': True,
        'clip_outliers': True,
        'outlier_threshold': 3.0,
        'validation_sample_rate': 1.0
    }
    
    # Adjust based on data size
    if n_sequences > 10000:
        config['validation_sample_rate'] = 0.5  # Sample for large datasets
    
    if n_sequences > 50000:
        config['validation_sample_rate'] = 0.1  # More aggressive sampling
    
    # Adjust based on target model
    if target_model == 'deep':
        config['normalization_method'] = 'standard'  # Deep models often prefer standard normalization
        config['outlier_threshold'] = 2.5  # More aggressive outlier handling
    
    # Adjust based on feature types
    well_log_features = [f for f in feature_names if f.upper() in WELL_LOG_RANGES]
    if len(well_log_features) == len(feature_names):
        config['validate_ranges'] = True  # All features are well logs
    else:
        config['validate_ranges'] = False  # Mixed feature types
    
    return config


# Export public interface
__all__ = [
    'validate_and_clean_input',
    'enhanced_normalize_data',
    'numerical_stability_check',
    'phase1_preprocessing_pipeline',
    'enhanced_validate_sequences',
    'get_recommended_preprocessing_config',
    'WELL_LOG_RANGES'
]
