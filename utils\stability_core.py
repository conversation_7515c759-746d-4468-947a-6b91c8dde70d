"""
Advanced Data Preprocessing & Training Stabilization Core Module

DEPRECATED: This module has been moved to core_code/ml/algorithms/preprocessing.py
Please update your imports to use the new location.

This module implements Phase 1 of the comprehensive stabilization strategy
to eliminate non-finite gradient issues and enhance numerical stability for
all deep learning models in the codebase.

Phase 1: Enhanced Data Preprocessing
- Advanced Input Validation & Cleaning
- Enhanced Normalization Pipeline
- Missing Value Encoding

Author: Advanced Preprocessing Pipeline
Date: 2025-07-26
"""

import warnings

# Issue deprecation warning
warnings.warn(
    "utils.stability_core is deprecated. Please use core_code.ml.algorithms.preprocessing instead.",
    DeprecationWarning,
    stacklevel=2
)

import numpy as np
import pandas as pd
import warnings
import logging
from typing import Dict, List, Tuple, Optional, Union, Any
from sklearn.preprocessing import StandardScaler, RobustScaler, QuantileTransformer
from sklearn.impute import SimpleImputer
import torch

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Well log type ranges for validation (based on common industry standards)
WELL_LOG_RANGES = {
    'GR': (0, 300),      # Gamma Ray (API units)
    'NPHI': (0, 1),      # Neutron Porosity (fraction)
    'RHOB': (1.5, 3.0),  # Bulk Density (g/cm3)
    'DT': (40, 200),     # Delta Time (us/ft)
    'RT': (0.1, 1000),   # Resistivity (ohm-m)
    'SP': (-200, 200),   # Spontaneous Potential (mV)
    'CALI': (6, 20),     # Caliper (inches)
    'PEF': (0, 10),      # Photoelectric Factor
    'MD': (0, 10000),    # Measured Depth (ft/m)
    'TVD': (0, 10000),   # True Vertical Depth (ft/m)
}

def validate_and_clean_input(sequences: np.ndarray, 
                           feature_names: List[str],
                           max_value_threshold: float = 1e10,
                           min_value_threshold: float = -1e10,
                           validate_ranges: bool = True) -> Tuple[np.ndarray, Dict[str, Any]]:
    """
    Comprehensive input validation and cleaning before model training.
    
    This function addresses the "Missing input validation" issue by detecting
    and handling non-finite values, numerical overflow conditions, and 
    validating data ranges per well log type.
    
    Args:
        sequences: Input sequences (n_sequences, seq_len, n_features)
        feature_names: List of feature names for validation
        max_value_threshold: Maximum allowed value (default: 1e10)
        min_value_threshold: Minimum allowed value (default: -1e10)
        validate_ranges: Whether to validate well log ranges
        
    Returns:
        Tuple of (cleaned_sequences, validation_report)
    """
    logger.info("🔍 Starting comprehensive input validation and cleaning...")
    
    if not isinstance(sequences, np.ndarray):
        sequences = np.array(sequences, dtype=np.float32)
    
    original_shape = sequences.shape
    n_sequences, seq_len, n_features = original_shape
    
    # Initialize validation report
    validation_report = {
        'original_shape': original_shape,
        'feature_names': feature_names,
        'issues_found': [],
        'corrections_applied': [],
        'data_quality_score': 0.0,
        'feature_statistics': {}
    }
    
    # Create a copy for cleaning
    cleaned_sequences = sequences.copy()
    total_elements = np.prod(original_shape)
    
    # 1. Check for non-finite values (inf, -inf, NaN)
    logger.info("   Checking for non-finite values...")
    inf_mask = np.isinf(sequences)
    inf_count = np.sum(inf_mask)
    
    if inf_count > 0:
        validation_report['issues_found'].append(f"Found {inf_count} infinite values")
        cleaned_sequences[inf_mask] = np.nan
        validation_report['corrections_applied'].append(f"Converted {inf_count} infinite values to NaN")
        logger.warning(f"   Found and converted {inf_count} infinite values to NaN")
    
    # 2. Check for extremely large values
    logger.info("   Checking for extremely large values...")
    extreme_mask = (np.abs(sequences) > max_value_threshold) & np.isfinite(sequences)
    extreme_count = np.sum(extreme_mask)
    
    if extreme_count > 0:
        validation_report['issues_found'].append(f"Found {extreme_count} extremely large values (>{max_value_threshold})")
        cleaned_sequences[extreme_mask] = np.nan
        validation_report['corrections_applied'].append(f"Converted {extreme_count} extreme values to NaN")
        logger.warning(f"   Found and converted {extreme_count} extremely large values to NaN")
    
    # 3. Check for extremely small values
    logger.info("   Checking for extremely small values...")
    small_mask = (sequences < min_value_threshold) & np.isfinite(sequences)
    small_count = np.sum(small_mask)
    
    if small_count > 0:
        validation_report['issues_found'].append(f"Found {small_count} extremely small values (<{min_value_threshold})")
        cleaned_sequences[small_mask] = np.nan
        validation_report['corrections_applied'].append(f"Converted {small_count} extreme small values to NaN")
        logger.warning(f"   Found and converted {small_count} extremely small values to NaN")
    
    # 4. Validate well log ranges if requested
    if validate_ranges and len(feature_names) == n_features:
        logger.info("   Validating well log ranges...")
        for i, feature_name in enumerate(feature_names):
            # Extract feature name (remove prefixes/suffixes if present)
            clean_name = feature_name.upper().replace('_NORM', '').replace('_SCALED', '')
            
            if clean_name in WELL_LOG_RANGES:
                min_val, max_val = WELL_LOG_RANGES[clean_name]
                feature_data = cleaned_sequences[:, :, i]
                valid_data = feature_data[np.isfinite(feature_data)]
                
                if len(valid_data) > 0:
                    out_of_range_mask = (feature_data < min_val) | (feature_data > max_val)
                    out_of_range_count = np.sum(out_of_range_mask & np.isfinite(feature_data))
                    
                    if out_of_range_count > 0:
                        validation_report['issues_found'].append(
                            f"Feature '{feature_name}': {out_of_range_count} values outside expected range [{min_val}, {max_val}]"
                        )
                        # Note: We don't automatically remove out-of-range values as they might be valid
                        # in some geological contexts, but we flag them for review
                        logger.warning(f"   Feature '{feature_name}': {out_of_range_count} values outside expected range")
    
    # 5. Calculate feature statistics
    logger.info("   Calculating feature statistics...")
    for i, feature_name in enumerate(feature_names):
        feature_data = cleaned_sequences[:, :, i]
        valid_data = feature_data[np.isfinite(feature_data)]
        
        if len(valid_data) > 0:
            validation_report['feature_statistics'][feature_name] = {
                'count': len(valid_data),
                'missing_count': np.sum(np.isnan(feature_data)),
                'missing_rate': np.sum(np.isnan(feature_data)) / feature_data.size,
                'mean': float(np.mean(valid_data)),
                'std': float(np.std(valid_data)),
                'min': float(np.min(valid_data)),
                'max': float(np.max(valid_data)),
                'q25': float(np.percentile(valid_data, 25)),
                'q75': float(np.percentile(valid_data, 75))
            }
        else:
            validation_report['feature_statistics'][feature_name] = {
                'count': 0,
                'missing_count': feature_data.size,
                'missing_rate': 1.0,
                'mean': np.nan,
                'std': np.nan,
                'min': np.nan,
                'max': np.nan,
                'q25': np.nan,
                'q75': np.nan
            }
    
    # 6. Calculate overall data quality score
    total_nan_count = np.sum(np.isnan(cleaned_sequences))
    nan_rate = total_nan_count / total_elements
    validation_report['data_quality_score'] = max(0.0, 1.0 - nan_rate)
    
    # 7. Final validation checks
    if np.any(np.isinf(cleaned_sequences)):
        logger.error("   ERROR: Infinite values still present after cleaning!")
        validation_report['issues_found'].append("CRITICAL: Infinite values remain after cleaning")
    
    if np.any(np.abs(cleaned_sequences[np.isfinite(cleaned_sequences)]) > max_value_threshold):
        logger.error("   ERROR: Extremely large values still present after cleaning!")
        validation_report['issues_found'].append("CRITICAL: Extreme values remain after cleaning")
    
    logger.info(f"✅ Input validation completed. Data quality score: {validation_report['data_quality_score']:.3f}")
    logger.info(f"   Total NaN rate: {nan_rate:.1%} ({total_nan_count}/{total_elements} elements)")
    
    return cleaned_sequences, validation_report


def robust_normalize_data(df: pd.DataFrame, 
                         columns: List[str], 
                         method: str = 'robust_standard',
                         winsorize_percentiles: Tuple[float, float] = (0.01, 0.99),
                         log_transform_features: Optional[List[str]] = None) -> Tuple[pd.DataFrame, Dict[str, Any], Dict[str, Any]]:
    """
    Enhanced normalization with multiple strategies for robust preprocessing.
    
    This function addresses scale mismatch issues and provides outlier-resistant
    normalization methods that are more stable for deep learning training.
    
    Args:
        df: Input dataframe
        columns: Columns to normalize
        method: Normalization method ('robust_standard', 'quantile', 'log_transform', 'winsorize')
        winsorize_percentiles: Percentiles for winsorization (lower, upper)
        log_transform_features: Features to apply log transformation
        
    Returns:
        Tuple of (normalized_df, scalers_dict, normalization_report)
    """
    logger.info(f"📊 Starting robust normalization with method: {method}")
    
    df_normalized = df.copy()
    scalers = {}
    normalization_report = {
        'method': method,
        'columns_processed': [],
        'columns_skipped': [],
        'normalization_stats': {},
        'warnings': []
    }
    
    for col in columns:
        if col not in df.columns:
            logger.warning(f"   Column '{col}' not found, skipping")
            normalization_report['columns_skipped'].append(col)
            continue
        
        # Get valid data for fitting
        valid_data = df[col].dropna()
        
        if len(valid_data) == 0:
            logger.warning(f"   No valid data for column '{col}', skipping")
            normalization_report['columns_skipped'].append(col)
            scalers[col] = None
            continue
        
        logger.info(f"   Processing column '{col}' with {len(valid_data)} valid values")
        
        try:
            if method == 'robust_standard':
                # Use RobustScaler (median and IQR based)
                scaler = RobustScaler()
                scaler.fit(valid_data.values.reshape(-1, 1))
                df_normalized[col] = scaler.transform(df[[col]])
                scalers[col] = scaler
                
            elif method == 'quantile':
                # Quantile normalization for skewed distributions
                scaler = QuantileTransformer(output_distribution='normal', random_state=42)
                scaler.fit(valid_data.values.reshape(-1, 1))
                df_normalized[col] = scaler.transform(df[[col]])
                scalers[col] = scaler
                
            elif method == 'log_transform':
                # Log transformation for highly skewed data
                if log_transform_features and col in log_transform_features:
                    # Apply log(1 + x) transformation for positive skewed data
                    min_val = valid_data.min()
                    if min_val <= 0:
                        # Shift data to positive range
                        shift_value = abs(min_val) + 1
                        df_normalized[col] = np.log1p(df[col] + shift_value)
                        scalers[col] = {'type': 'log_shift', 'shift': shift_value}
                    else:
                        df_normalized[col] = np.log1p(df[col])
                        scalers[col] = {'type': 'log', 'shift': 0}
                else:
                    # Fall back to robust standard
                    scaler = RobustScaler()
                    scaler.fit(valid_data.values.reshape(-1, 1))
                    df_normalized[col] = scaler.transform(df[[col]])
                    scalers[col] = scaler
                    
            elif method == 'winsorize':
                # Winsorization + StandardScaler
                lower_bound, upper_bound = np.quantile(valid_data, winsorize_percentiles)
                
                # Apply winsorization
                df_winsorized = df[col].copy()
                df_winsorized = df_winsorized.clip(lower=lower_bound, upper=upper_bound)
                
                # Then apply standard scaling
                scaler = StandardScaler()
                valid_winsorized = df_winsorized.dropna()
                scaler.fit(valid_winsorized.values.reshape(-1, 1))
                df_normalized[col] = scaler.transform(df_winsorized.values.reshape(-1, 1)).flatten()
                
                scalers[col] = {
                    'scaler': scaler,
                    'winsorize_bounds': (lower_bound, upper_bound)
                }
                
            else:
                raise ValueError(f"Unknown normalization method: {method}")
            
            # Calculate normalization statistics
            normalized_valid = df_normalized[col].dropna()
            normalization_report['normalization_stats'][col] = {
                'original_mean': float(valid_data.mean()),
                'original_std': float(valid_data.std()),
                'original_range': (float(valid_data.min()), float(valid_data.max())),
                'normalized_mean': float(normalized_valid.mean()),
                'normalized_std': float(normalized_valid.std()),
                'normalized_range': (float(normalized_valid.min()), float(normalized_valid.max())),
                'valid_count': len(valid_data),
                'total_count': len(df[col]),
                'missing_rate': (len(df[col]) - len(valid_data)) / len(df[col])
            }
            
            normalization_report['columns_processed'].append(col)
            logger.info(f"   ✅ Normalized '{col}': {len(valid_data)}/{len(df[col])} valid values")
            
        except Exception as e:
            logger.error(f"   ❌ Error normalizing column '{col}': {str(e)}")
            normalization_report['warnings'].append(f"Failed to normalize '{col}': {str(e)}")
            normalization_report['columns_skipped'].append(col)
            scalers[col] = None
    
    logger.info(f"✅ Robust normalization completed. Processed {len(normalization_report['columns_processed'])} columns")

    return df_normalized, scalers, normalization_report


def encode_missing_values(sequences: np.ndarray,
                         method: str = 'learnable_embedding',
                         feature_names: Optional[List[str]] = None,
                         embedding_dim: int = 8,
                         uncertainty_threshold: float = 0.1) -> Tuple[np.ndarray, Dict[str, Any]]:
    """
    Replace raw NaN values with model-friendly representations.

    This function addresses the "Raw NaN propagation" issue by encoding missing
    values in ways that are compatible with deep learning models and prevent
    gradient explosions.

    Args:
        sequences: Input sequences (n_sequences, seq_len, n_features)
        method: Encoding method ('learnable_embedding', 'masking_tokens', 'forward_fill', 'statistical_imputation')
        feature_names: List of feature names
        embedding_dim: Dimension for learnable embeddings
        uncertainty_threshold: Threshold for uncertainty indicators

    Returns:
        Tuple of (encoded_sequences, encoding_metadata)
    """
    logger.info(f"❓ Starting missing value encoding with method: {method}")

    if not isinstance(sequences, np.ndarray):
        sequences = np.array(sequences, dtype=np.float32)

    original_shape = sequences.shape
    n_sequences, seq_len, n_features = original_shape

    # Initialize encoding metadata
    encoding_metadata = {
        'method': method,
        'original_shape': original_shape,
        'missing_count_before': int(np.sum(np.isnan(sequences))),
        'missing_rate_before': float(np.sum(np.isnan(sequences)) / np.prod(original_shape)),
        'feature_missing_stats': {},
        'encoding_parameters': {}
    }

    # Calculate per-feature missing statistics
    for i in range(n_features):
        feature_name = feature_names[i] if feature_names and i < len(feature_names) else f'feature_{i}'
        feature_data = sequences[:, :, i]
        missing_count = np.sum(np.isnan(feature_data))
        encoding_metadata['feature_missing_stats'][feature_name] = {
            'missing_count': int(missing_count),
            'missing_rate': float(missing_count / feature_data.size),
            'total_elements': int(feature_data.size)
        }

    # Create encoded sequences
    encoded_sequences = sequences.copy()

    if method == 'learnable_embedding':
        # Replace NaN with learnable embedding tokens
        # Use small random values that can be learned during training
        logger.info("   Applying learnable embedding encoding...")

        np.random.seed(42)  # For reproducibility
        missing_mask = np.isnan(sequences)

        # Generate learnable tokens (small random values)
        embedding_tokens = np.random.normal(0, 0.01, size=np.sum(missing_mask))
        encoded_sequences[missing_mask] = embedding_tokens

        encoding_metadata['encoding_parameters'] = {
            'embedding_type': 'small_random',
            'embedding_std': 0.01,
            'random_seed': 42
        }

    elif method == 'masking_tokens':
        # Use special masking tokens compatible with attention mechanisms
        logger.info("   Applying masking token encoding...")

        # Use a special value that's clearly distinguishable (e.g., -999)
        mask_token_value = -999.0
        missing_mask = np.isnan(sequences)
        encoded_sequences[missing_mask] = mask_token_value

        encoding_metadata['encoding_parameters'] = {
            'mask_token_value': mask_token_value,
            'attention_mask_available': True
        }

    elif method == 'forward_fill':
        # Forward fill with uncertainty indicators
        logger.info("   Applying forward fill with uncertainty...")

        for seq_idx in range(n_sequences):
            for feat_idx in range(n_features):
                sequence_feature = encoded_sequences[seq_idx, :, feat_idx]

                # Forward fill within each sequence
                last_valid_value = None
                for t in range(seq_len):
                    if np.isnan(sequence_feature[t]):
                        if last_valid_value is not None:
                            # Add uncertainty (small noise) to forward-filled values
                            uncertainty = np.random.normal(0, uncertainty_threshold)
                            encoded_sequences[seq_idx, t, feat_idx] = last_valid_value + uncertainty
                        else:
                            # If no previous valid value, use feature mean or 0
                            feature_data = sequences[:, :, feat_idx]
                            valid_data = feature_data[np.isfinite(feature_data)]
                            if len(valid_data) > 0:
                                encoded_sequences[seq_idx, t, feat_idx] = np.mean(valid_data)
                            else:
                                encoded_sequences[seq_idx, t, feat_idx] = 0.0
                    else:
                        last_valid_value = sequence_feature[t]

        encoding_metadata['encoding_parameters'] = {
            'uncertainty_threshold': uncertainty_threshold,
            'fallback_strategy': 'feature_mean_or_zero'
        }

    elif method == 'statistical_imputation':
        # Statistical imputation with confidence scores
        logger.info("   Applying statistical imputation...")

        for feat_idx in range(n_features):
            feature_data = sequences[:, :, feat_idx]
            valid_data = feature_data[np.isfinite(feature_data)]

            if len(valid_data) > 0:
                # Use median for robustness
                imputation_value = np.median(valid_data)
                missing_mask = np.isnan(feature_data)
                encoded_sequences[:, :, feat_idx][missing_mask] = imputation_value

                feature_name = feature_names[feat_idx] if feature_names and feat_idx < len(feature_names) else f'feature_{feat_idx}'
                encoding_metadata['feature_missing_stats'][feature_name]['imputation_value'] = float(imputation_value)
            else:
                # If no valid data, use 0
                missing_mask = np.isnan(feature_data)
                encoded_sequences[:, :, feat_idx][missing_mask] = 0.0

        encoding_metadata['encoding_parameters'] = {
            'imputation_strategy': 'median',
            'fallback_value': 0.0
        }

    else:
        raise ValueError(f"Unknown encoding method: {method}")

    # Final validation
    missing_count_after = np.sum(np.isnan(encoded_sequences))
    encoding_metadata['missing_count_after'] = int(missing_count_after)
    encoding_metadata['missing_rate_after'] = float(missing_count_after / np.prod(original_shape))

    # Check for non-finite values
    if np.any(np.isinf(encoded_sequences)):
        logger.warning("   WARNING: Infinite values detected after encoding!")
        encoding_metadata['warnings'] = ['Infinite values present after encoding']

    logger.info(f"✅ Missing value encoding completed.")
    logger.info(f"   Missing values: {encoding_metadata['missing_count_before']} → {missing_count_after}")
    logger.info(f"   Missing rate: {encoding_metadata['missing_rate_before']:.1%} → {encoding_metadata['missing_rate_after']:.1%}")

    return encoded_sequences, encoding_metadata


def numerical_stability_check(data: Union[np.ndarray, torch.Tensor],
                            name: str = "data",
                            max_abs_value: float = 1e6) -> Dict[str, Any]:
    """
    Comprehensive numerical stability check for tensors/arrays.

    Args:
        data: Input data to check
        name: Name for logging purposes
        max_abs_value: Maximum absolute value threshold

    Returns:
        Dictionary with stability metrics
    """
    if isinstance(data, torch.Tensor):
        data_np = data.detach().cpu().numpy()
    else:
        data_np = np.array(data)

    stability_report = {
        'name': name,
        'shape': data_np.shape,
        'dtype': str(data_np.dtype),
        'is_stable': True,
        'issues': []
    }

    # Check for NaN values
    nan_count = np.sum(np.isnan(data_np))
    if nan_count > 0:
        stability_report['is_stable'] = False
        stability_report['issues'].append(f"Contains {nan_count} NaN values")

    # Check for infinite values
    inf_count = np.sum(np.isinf(data_np))
    if inf_count > 0:
        stability_report['is_stable'] = False
        stability_report['issues'].append(f"Contains {inf_count} infinite values")

    # Check for extremely large values
    finite_data = data_np[np.isfinite(data_np)]
    if len(finite_data) > 0:
        max_abs = np.max(np.abs(finite_data))
        if max_abs > max_abs_value:
            stability_report['is_stable'] = False
            stability_report['issues'].append(f"Maximum absolute value {max_abs:.2e} exceeds threshold {max_abs_value:.2e}")

        stability_report['statistics'] = {
            'mean': float(np.mean(finite_data)),
            'std': float(np.std(finite_data)),
            'min': float(np.min(finite_data)),
            'max': float(np.max(finite_data)),
            'max_abs': float(max_abs)
        }

    return stability_report


def batch_diagnostics(batch_data: Union[np.ndarray, torch.Tensor],
                     batch_idx: int,
                     feature_names: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Perform batch-level diagnostics for problematic sample detection.

    Args:
        batch_data: Batch data to analyze
        batch_idx: Batch index for identification
        feature_names: List of feature names

    Returns:
        Dictionary with batch diagnostics
    """
    if isinstance(batch_data, torch.Tensor):
        data_np = batch_data.detach().cpu().numpy()
    else:
        data_np = np.array(batch_data)

    diagnostics = {
        'batch_idx': batch_idx,
        'shape': data_np.shape,
        'is_problematic': False,
        'issues': [],
        'feature_diagnostics': {}
    }

    # Overall batch checks
    stability_check = numerical_stability_check(data_np, f"batch_{batch_idx}")
    if not stability_check['is_stable']:
        diagnostics['is_problematic'] = True
        diagnostics['issues'].extend(stability_check['issues'])

    # Per-feature diagnostics
    if len(data_np.shape) >= 3:  # (batch_size, seq_len, n_features)
        n_features = data_np.shape[-1]
        for i in range(n_features):
            feature_name = feature_names[i] if feature_names and i < len(feature_names) else f'feature_{i}'
            feature_data = data_np[:, :, i]

            feature_stability = numerical_stability_check(feature_data, f"{feature_name}_batch_{batch_idx}")
            diagnostics['feature_diagnostics'][feature_name] = feature_stability

            if not feature_stability['is_stable']:
                diagnostics['is_problematic'] = True

    return diagnostics


def generate_preprocessing_report(validation_report: Dict[str, Any],
                                normalization_report: Dict[str, Any],
                                encoding_report: Dict[str, Any]) -> str:
    """
    Generate a comprehensive preprocessing report.

    Args:
        validation_report: Report from validate_and_clean_input
        normalization_report: Report from robust_normalize_data
        encoding_report: Report from encode_missing_values

    Returns:
        Formatted report string
    """
    report = []
    report.append("=" * 80)
    report.append(" PHASE 1: ADVANCED DATA PREPROCESSING REPORT")
    report.append("=" * 80)

    # Validation section
    report.append(f"\n🔍 INPUT VALIDATION & CLEANING")
    report.append(f"   Data Quality Score: {validation_report['data_quality_score']:.3f}")
    report.append(f"   Issues Found: {len(validation_report['issues_found'])}")
    for issue in validation_report['issues_found']:
        report.append(f"     • {issue}")
    report.append(f"   Corrections Applied: {len(validation_report['corrections_applied'])}")
    for correction in validation_report['corrections_applied']:
        report.append(f"     • {correction}")

    # Normalization section
    report.append(f"\n📊 ROBUST NORMALIZATION")
    report.append(f"   Method: {normalization_report['method']}")
    report.append(f"   Columns Processed: {len(normalization_report['columns_processed'])}")
    report.append(f"   Columns Skipped: {len(normalization_report['columns_skipped'])}")

    # Encoding section
    report.append(f"\n❓ MISSING VALUE ENCODING")
    report.append(f"   Method: {encoding_report['method']}")
    report.append(f"   Missing Rate: {encoding_report['missing_rate_before']:.1%} → {encoding_report['missing_rate_after']:.1%}")
    report.append(f"   Missing Count: {encoding_report['missing_count_before']} → {encoding_report['missing_count_after']}")

    # Feature statistics
    report.append(f"\n📈 FEATURE STATISTICS")
    for feature_name, stats in validation_report['feature_statistics'].items():
        report.append(f"   {feature_name}:")
        report.append(f"     • Valid: {stats['count']}/{stats['count'] + stats['missing_count']} ({(1-stats['missing_rate'])*100:.1f}%)")
        if not np.isnan(stats['mean']):
            report.append(f"     • Range: [{stats['min']:.3f}, {stats['max']:.3f}]")
            report.append(f"     • Mean±Std: {stats['mean']:.3f}±{stats['std']:.3f}")

    report.append(f"\n✅ Phase 1 preprocessing completed successfully!")
    report.append("   Ready for stable deep learning training.")

    return "\n".join(report)


def phase1_preprocessing_pipeline(sequences: np.ndarray,
                                feature_names: List[str],
                                normalization_method: str = 'robust_standard',
                                missing_encoding_method: str = 'learnable_embedding',
                                validate_ranges: bool = True,
                                generate_report: bool = True) -> Tuple[np.ndarray, Dict[str, Any]]:
    """
    Complete Phase 1 preprocessing pipeline that integrates all components.

    This function provides a unified interface to apply all Phase 1 preprocessing
    steps in the correct order for maximum stability and effectiveness.

    Args:
        sequences: Input sequences (n_sequences, seq_len, n_features)
        feature_names: List of feature names
        normalization_method: Method for robust normalization
        missing_encoding_method: Method for missing value encoding
        validate_ranges: Whether to validate well log ranges
        generate_report: Whether to generate comprehensive report

    Returns:
        Tuple of (processed_sequences, processing_metadata)
    """
    logger.info("🚀 Starting Phase 1 Advanced Preprocessing Pipeline...")

    processing_metadata = {
        'pipeline_version': 'Phase 1',
        'input_shape': sequences.shape,
        'feature_names': feature_names,
        'parameters': {
            'normalization_method': normalization_method,
            'missing_encoding_method': missing_encoding_method,
            'validate_ranges': validate_ranges
        },
        'reports': {}
    }

    # Step 1: Input Validation & Cleaning
    logger.info("\n🔍 Step 1: Advanced Input Validation & Cleaning...")
    cleaned_sequences, validation_report = validate_and_clean_input(
        sequences, feature_names, validate_ranges=validate_ranges
    )
    processing_metadata['reports']['validation'] = validation_report

    # Step 2: Missing Value Encoding (before normalization to handle NaNs properly)
    logger.info(f"\n❓ Step 2: Missing Value Encoding ({missing_encoding_method})...")
    encoded_sequences, encoding_report = encode_missing_values(
        cleaned_sequences, method=missing_encoding_method, feature_names=feature_names
    )
    processing_metadata['reports']['encoding'] = encoding_report

    # Step 3: Convert to DataFrame for normalization (if needed)
    if normalization_method != 'none':
        logger.info(f"\n📊 Step 3: Robust Normalization ({normalization_method})...")

        # Reshape sequences to 2D for normalization
        n_sequences, seq_len, n_features = encoded_sequences.shape
        sequences_2d = encoded_sequences.reshape(-1, n_features)

        # Create DataFrame
        df_for_norm = pd.DataFrame(sequences_2d, columns=feature_names)

        # Apply robust normalization
        df_normalized, scalers, normalization_report = robust_normalize_data(
            df_for_norm, feature_names, method=normalization_method
        )

        # Reshape back to 3D
        normalized_sequences = df_normalized.values.reshape(n_sequences, seq_len, n_features)
        processing_metadata['reports']['normalization'] = normalization_report
        processing_metadata['scalers'] = scalers
    else:
        normalized_sequences = encoded_sequences
        processing_metadata['reports']['normalization'] = {'method': 'none', 'message': 'Normalization skipped'}
        processing_metadata['scalers'] = {}

    # Step 4: Final Stability Check
    logger.info("\n🔧 Step 4: Final Numerical Stability Check...")
    final_stability = numerical_stability_check(normalized_sequences, "final_processed_sequences")
    processing_metadata['final_stability'] = final_stability

    if not final_stability['is_stable']:
        logger.warning("⚠️  WARNING: Final sequences may have stability issues!")
        for issue in final_stability['issues']:
            logger.warning(f"   • {issue}")
    else:
        logger.info("✅ Final sequences pass all stability checks")

    # Step 5: Generate Comprehensive Report
    if generate_report:
        logger.info("\n📋 Step 5: Generating Comprehensive Report...")
        comprehensive_report = generate_preprocessing_report(
            validation_report,
            processing_metadata['reports']['normalization'],
            encoding_report
        )
        processing_metadata['comprehensive_report'] = comprehensive_report
        print(comprehensive_report)

    # Final metadata
    processing_metadata['output_shape'] = normalized_sequences.shape
    processing_metadata['processing_complete'] = True

    logger.info(f"\n🎉 Phase 1 preprocessing pipeline completed successfully!")
    logger.info(f"   Input shape: {sequences.shape} → Output shape: {normalized_sequences.shape}")
    logger.info(f"   Data quality score: {validation_report['data_quality_score']:.3f}")
    logger.info(f"   Ready for stable deep learning training!")

    return normalized_sequences, processing_metadata


# Convenience functions for backward compatibility and easy integration

def enhanced_validate_sequences(sequences: np.ndarray,
                              feature_names: List[str],
                              batch_idx: Optional[int] = None) -> bool:
    """
    Quick validation function for sequences before training.

    Args:
        sequences: Input sequences to validate
        feature_names: List of feature names
        batch_idx: Optional batch index for logging

    Returns:
        True if sequences are stable, False otherwise
    """
    if batch_idx is not None:
        diagnostics = batch_diagnostics(sequences, batch_idx, feature_names)
        return not diagnostics['is_problematic']
    else:
        stability = numerical_stability_check(sequences, "input_sequences")
        return stability['is_stable']


def get_recommended_preprocessing_config(dataset_size: int,
                                       missing_rate: float,
                                       feature_types: Optional[List[str]] = None) -> Dict[str, str]:
    """
    Get recommended preprocessing configuration based on dataset characteristics.

    Args:
        dataset_size: Number of sequences in dataset
        missing_rate: Overall missing value rate
        feature_types: Optional list of feature types (e.g., ['GR', 'NPHI', 'RHOB'])

    Returns:
        Dictionary with recommended configuration
    """
    config = {}

    # Normalization method recommendation
    if missing_rate > 0.3:
        config['normalization_method'] = 'robust_standard'  # More robust to outliers
    elif dataset_size > 10000:
        config['normalization_method'] = 'quantile'  # Better for large datasets
    else:
        config['normalization_method'] = 'robust_standard'

    # Missing value encoding recommendation
    if missing_rate > 0.5:
        config['missing_encoding_method'] = 'statistical_imputation'  # Conservative for high missing rates
    elif missing_rate > 0.2:
        config['missing_encoding_method'] = 'forward_fill'  # Good for time series
    else:
        config['missing_encoding_method'] = 'learnable_embedding'  # Best for learning

    # Range validation recommendation
    if feature_types and any(ft in WELL_LOG_RANGES for ft in feature_types):
        config['validate_ranges'] = True
    else:
        config['validate_ranges'] = False

    return config


class UniversalGradientClipper:
    """
    Unified gradient clipping for all deep learning models.
    Implements adaptive clipping based on model type as outlined in Advanced_Preprocess_Stabilize.md
    """

    def __init__(self, model_type: str = 'transformer', max_norm: float = None):
        """
        Initialize gradient clipper with model-specific defaults.

        Args:
            model_type: Type of model ('transformer', 'rnn', 'autoencoder', 'unet')
            max_norm: Maximum gradient norm (None for model-specific defaults)
        """
        self.model_type = model_type.lower()

        # Model-specific gradient clipping norms from Advanced_Preprocess_Stabilize.md
        default_norms = {
            'transformer': 0.5,      # Reduced for attention stability
            'saits': 1.0,
            'brits': 1.5,
            'mrnn': 1.5,
            'autoencoder': 2.0,
            'unet': 2.0,
            'default': 1.0
        }

        self.max_norm = max_norm or default_norms.get(self.model_type, default_norms['default'])
        logger.info(f"🔧 Initialized gradient clipper for {model_type} with max_norm={self.max_norm}")

    def clip_gradients(self, model_parameters, check_finite: bool = True) -> Dict[str, Any]:
        """
        Clip gradients with finite checking and diagnostics.

        Args:
            model_parameters: Model parameters to clip
            check_finite: Whether to check for finite gradients

        Returns:
            Dictionary with clipping diagnostics
        """
        diagnostics = {
            'total_norm': 0.0,
            'clipped': False,
            'finite_gradients': True,
            'gradient_count': 0,
            'zero_gradients': 0
        }

        # Check for finite gradients first
        if check_finite:
            for param in model_parameters:
                if param.grad is not None:
                    diagnostics['gradient_count'] += 1
                    if not torch.isfinite(param.grad).all():
                        diagnostics['finite_gradients'] = False
                        logger.warning(f"⚠️ Non-finite gradients detected in parameter")
                        return diagnostics
                    if torch.norm(param.grad) == 0:
                        diagnostics['zero_gradients'] += 1

        # Clip gradients if they are finite
        if diagnostics['finite_gradients']:
            total_norm = torch.nn.utils.clip_grad_norm_(model_parameters, self.max_norm)
            diagnostics['total_norm'] = float(total_norm)
            diagnostics['clipped'] = total_norm > self.max_norm

            if diagnostics['clipped']:
                logger.debug(f"🔧 Gradients clipped: norm {total_norm:.3f} → {self.max_norm}")

        return diagnostics


class AdaptiveLRScheduler:
    """
    Model-specific learning rate scheduling with warmup.
    Implements scheduling strategies from Advanced_Preprocess_Stabilize.md
    """

    def __init__(self, optimizer, model_type: str = 'transformer',
                 warmup_steps: int = 1000, total_steps: int = None):
        """
        Initialize adaptive learning rate scheduler.

        Args:
            optimizer: PyTorch optimizer
            model_type: Type of model for scheduling strategy
            warmup_steps: Number of warmup steps
            total_steps: Total training steps (for cosine scheduling)
        """
        self.optimizer = optimizer
        self.model_type = model_type.lower()
        self.warmup_steps = warmup_steps
        self.total_steps = total_steps or 10000  # Default total steps
        self.current_step = 0
        self.base_lr = optimizer.param_groups[0]['lr']

        logger.info(f"🚀 Initialized {model_type} LR scheduler: warmup={warmup_steps}, base_lr={self.base_lr}")

    def step(self):
        """Update learning rate based on current step and model type."""
        self.current_step += 1

        if self.model_type == 'transformer':
            # Transformer: Warmup + Cosine decay
            if self.current_step <= self.warmup_steps:
                # Linear warmup
                lr = self.base_lr * (self.current_step / self.warmup_steps)
            else:
                # Cosine decay
                progress = (self.current_step - self.warmup_steps) / (self.total_steps - self.warmup_steps)
                lr = self.base_lr * 0.5 * (1 + np.cos(np.pi * progress))

        elif self.model_type in ['saits', 'brits']:
            # SAITS/BRITS: Linear warmup + Exponential decay
            if self.current_step <= self.warmup_steps:
                lr = self.base_lr * (self.current_step / self.warmup_steps)
            else:
                decay_rate = 0.95
                lr = self.base_lr * (decay_rate ** ((self.current_step - self.warmup_steps) // 100))

        else:
            # Default: Simple warmup + plateau
            if self.current_step <= self.warmup_steps:
                lr = self.base_lr * (self.current_step / self.warmup_steps)
            else:
                lr = self.base_lr

        # Update optimizer learning rate
        for param_group in self.optimizer.param_groups:
            param_group['lr'] = lr

        return lr


def enhanced_training_step(model, batch_data, batch_target, batch_mask, optimizer,
                          loss_fn, gradient_clipper: UniversalGradientClipper,
                          scaler=None, batch_idx: int = 0,
                          feature_names: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Standardized training step with stability checks.
    Implements the enhanced training step from Advanced_Preprocess_Stabilize.md
    """
    step_diagnostics = {
        'loss': 0.0,
        'step_successful': False,
        'skip_reason': None,
        'gradient_diagnostics': {},
        'batch_diagnostics': {}
    }

    try:
        # Step 1: Pre-forward input validation
        if feature_names:
            batch_stable = validate_batch_before_training(batch_data, batch_idx, feature_names)
            if not batch_stable:
                step_diagnostics['skip_reason'] = 'unstable_batch'
                return step_diagnostics

        # Step 2: Forward pass
        if scaler is not None:
            # Mixed precision forward pass
            with torch.autocast(device_type='cuda' if torch.cuda.is_available() else 'cpu'):
                predictions = model(batch_data)
                loss = loss_fn(predictions, batch_target, batch_mask)
        else:
            # Standard precision forward pass
            predictions = model(batch_data)
            loss = loss_fn(predictions, batch_target, batch_mask)

        # Validate loss
        if not torch.isfinite(loss):
            step_diagnostics['skip_reason'] = 'non_finite_loss'
            logger.warning(f"⚠️ Non-finite loss at batch {batch_idx}: {loss.item()}")
            return step_diagnostics

        step_diagnostics['loss'] = float(loss.item())

        # Step 3: Backward pass
        optimizer.zero_grad()

        if scaler is not None:
            # Mixed precision backward pass
            scaler.scale(loss).backward()
            scaler.unscale_(optimizer)
        else:
            # Standard precision backward pass
            loss.backward()

        # Step 4: Gradient finite checking and clipping
        grad_diagnostics = gradient_clipper.clip_gradients(model.parameters(), check_finite=True)
        step_diagnostics['gradient_diagnostics'] = grad_diagnostics

        if not grad_diagnostics['finite_gradients']:
            step_diagnostics['skip_reason'] = 'non_finite_gradients'
            logger.warning(f"⚠️ Non-finite gradients at batch {batch_idx}")
            return step_diagnostics

        # Step 5: Optimizer step
        if scaler is not None:
            scaler.step(optimizer)
            scaler.update()
        else:
            optimizer.step()

        step_diagnostics['step_successful'] = True

    except Exception as e:
        step_diagnostics['skip_reason'] = f'training_error: {str(e)}'
        logger.error(f"❌ Training error at batch {batch_idx}: {e}")

    return step_diagnostics


def validate_batch_before_training(batch_data: torch.Tensor,
                                 batch_idx: int,
                                 feature_names: List[str]) -> bool:
    """
    Validate a batch before training to prevent non-finite gradient issues.
    Enhanced version of the function from ml_core_phase1_integration.py
    """
    try:
        # Convert to numpy for validation
        if isinstance(batch_data, torch.Tensor):
            batch_np = batch_data.detach().cpu().numpy()
        else:
            batch_np = np.array(batch_data)

        # Check basic shape
        if len(batch_np.shape) != 3:
            logger.warning(f"⚠️ Batch {batch_idx}: Invalid shape {batch_np.shape}")
            return False

        # Check for non-finite values
        finite_count = np.sum(np.isfinite(batch_np))
        total_count = np.prod(batch_np.shape)
        finite_rate = finite_count / total_count

        if finite_rate < 0.1:  # Less than 10% finite values
            logger.warning(f"⚠️ Batch {batch_idx}: Only {finite_rate:.1%} finite values")
            return False

        # Check for extreme values
        finite_data = batch_np[np.isfinite(batch_np)]
        if len(finite_data) > 0:
            max_abs = np.max(np.abs(finite_data))
            if max_abs > 1e6:
                logger.warning(f"⚠️ Batch {batch_idx}: Extreme values detected (max_abs={max_abs:.2e})")
                return False

        return True

    except Exception as e:
        logger.error(f"❌ Batch validation error for batch {batch_idx}: {e}")
        return False


# Export main functions for easy import
__all__ = [
    'validate_and_clean_input',
    'robust_normalize_data',
    'encode_missing_values',
    'numerical_stability_check',
    'batch_diagnostics',
    'generate_preprocessing_report',
    'phase1_preprocessing_pipeline',
    'enhanced_validate_sequences',
    'get_recommended_preprocessing_config',
    'UniversalGradientClipper',
    'AdaptiveLRScheduler',
    'enhanced_training_step',
    'validate_batch_before_training',
    'WELL_LOG_RANGES'
]
