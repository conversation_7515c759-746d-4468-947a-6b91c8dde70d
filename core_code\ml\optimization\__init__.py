"""
ML Optimization Package

This package contains performance optimization utilities for machine learning:
- gpu.py: GPU acceleration and CUDA utilities
- memory.py: Memory optimization and management
- performance.py: Performance monitoring and profiling

These utilities provide comprehensive performance optimization for ML workflows.
"""

# Import optimization utilities with safe fallbacks
try:
    from .gpu import GPUAccelerator, get_gpu_accelerator, safe_cuda_empty_cache
    GPU_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ GPU optimization not available: {e}")
    GPUAccelerator = None
    get_gpu_accelerator = None
    safe_cuda_empty_cache = None
    GPU_AVAILABLE = False

try:
    from .memory import MemoryOptimizer, optimize_batch_size, enable_gradient_checkpointing
    MEMORY_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Memory optimization not available: {e}")
    MemoryOptimizer = None
    optimize_batch_size = None
    enable_gradient_checkpointing = None
    MEMORY_AVAILABLE = False

try:
    from .performance import PerformanceMonitor, profile_function, benchmark_model
    PERFORMANCE_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Performance monitoring not available: {e}")
    PerformanceMonitor = None
    profile_function = None
    benchmark_model = None
    PERFORMANCE_AVAILABLE = False

# Export public interface
__all__ = [
    # GPU optimization
    'GPUAccelerator',
    'get_gpu_accelerator',
    'safe_cuda_empty_cache',
    
    # Memory optimization
    'MemoryOptimizer',
    'optimize_batch_size',
    'enable_gradient_checkpointing',
    
    # Performance monitoring
    'PerformanceMonitor',
    'profile_function',
    'benchmark_model',
    
    # Status flags
    'GPU_AVAILABLE',
    'MEMORY_AVAILABLE',
    'PERFORMANCE_AVAILABLE'
]

print("ML optimization package initialized")
