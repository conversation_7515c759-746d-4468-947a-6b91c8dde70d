"""
ML Integration Package

This package contains advanced ML integrations and enhancements:
- phase1_core.py: Phase 1 enhanced ML core with performance optimizations

These integrations provide enhanced functionality while maintaining backward compatibility
with the existing ML pipeline.
"""

# Import Phase 1 integration with safe fallbacks
try:
    from .phase1_core import (
        impute_logs_deep_phase1,
        impute_logs_deep_phase1_optimized,
        impute_logs_deep_phase1_safe,
        OptimizationConfig,
        OPTIMIZATION_CONFIGS
    )
    PHASE1_INTEGRATION_AVAILABLE = True
    print("Phase 1 ML integration loaded successfully")
except ImportError as e:
    print(f"⚠️ Phase 1 integration not available: {e}")
    impute_logs_deep_phase1 = None
    impute_logs_deep_phase1_optimized = None
    impute_logs_deep_phase1_safe = None
    OptimizationConfig = None
    OPTIMIZATION_CONFIGS = None
    PHASE1_INTEGRATION_AVAILABLE = False

# Export public interface
__all__ = [
    # Phase 1 functions
    'impute_logs_deep_phase1',
    'impute_logs_deep_phase1_optimized',
    'impute_logs_deep_phase1_safe',
    
    # Configuration
    'OptimizationConfig',
    'OPTIMIZATION_CONFIGS',
    
    # Status
    'PHASE1_INTEGRATION_AVAILABLE'
]

if PHASE1_INTEGRATION_AVAILABLE:
    print("ML integration package initialized with Phase 1 enhancements")
else:
    print("ML integration package initialized (Phase 1 enhancements not available)")
