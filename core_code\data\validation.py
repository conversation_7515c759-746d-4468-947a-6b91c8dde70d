"""
Data Validation Utilities

This module provides comprehensive data validation functionality for well log data.
Includes quality checks, leakage detection, and distribution validation.

Key Features:
- Data quality assessment
- Data leakage detection
- Feature distribution validation
- Well log specific validation rules
- Statistical validation tests

Author: Data Processing Core
"""

import numpy as np
import pandas as pd
import warnings
from typing import Dict, List, Tuple, Optional, Union, Any
from scipy import stats

# Optional dependencies with graceful fallbacks
try:
    from sklearn.feature_selection import mutual_info_regression
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    warnings.warn("scikit-learn not available. Some validation features will be disabled.")


def validate_data_quality(data: Union[pd.DataFrame, np.ndarray],
                         feature_names: Optional[List[str]] = None,
                         quality_thresholds: Optional[Dict[str, float]] = None) -> Dict[str, Any]:
    """
    Comprehensive data quality validation.
    
    Args:
        data: Input data to validate
        feature_names: Optional feature names
        quality_thresholds: Custom quality thresholds
        
    Returns:
        Data quality report dictionary
    """
    # Default quality thresholds
    default_thresholds = {
        'missing_percentage_max': 50.0,  # Maximum allowed missing percentage
        'duplicate_percentage_max': 10.0,  # Maximum allowed duplicate percentage
        'outlier_percentage_max': 5.0,   # Maximum allowed outlier percentage
        'variance_min': 1e-6,            # Minimum variance for features
        'correlation_max': 0.95          # Maximum correlation between features
    }
    
    thresholds = quality_thresholds if quality_thresholds else default_thresholds
    
    # Convert to DataFrame if needed
    if isinstance(data, np.ndarray):
        if feature_names:
            df = pd.DataFrame(data.reshape(-1, data.shape[-1]), columns=feature_names)
        else:
            df = pd.DataFrame(data.reshape(-1, data.shape[-1]))
    else:
        df = data.copy()
    
    quality_report = {
        'overall_quality_score': 0.0,
        'is_high_quality': False,
        'issues': [],
        'warnings': [],
        'statistics': {},
        'recommendations': []
    }
    
    # 1. Missing value analysis
    missing_stats = {}
    for col in df.columns:
        missing_count = df[col].isna().sum()
        missing_percentage = (missing_count / len(df)) * 100
        missing_stats[col] = {
            'count': missing_count,
            'percentage': missing_percentage
        }
        
        if missing_percentage > thresholds['missing_percentage_max']:
            quality_report['issues'].append(
                f"Column {col}: {missing_percentage:.1f}% missing values (threshold: {thresholds['missing_percentage_max']}%)"
            )
    
    quality_report['statistics']['missing_values'] = missing_stats
    
    # 2. Duplicate analysis
    duplicate_count = df.duplicated().sum()
    duplicate_percentage = (duplicate_count / len(df)) * 100
    quality_report['statistics']['duplicates'] = {
        'count': duplicate_count,
        'percentage': duplicate_percentage
    }
    
    if duplicate_percentage > thresholds['duplicate_percentage_max']:
        quality_report['issues'].append(
            f"High duplicate rate: {duplicate_percentage:.1f}% (threshold: {thresholds['duplicate_percentage_max']}%)"
        )
    
    # 3. Outlier analysis
    numeric_columns = df.select_dtypes(include=[np.number]).columns
    outlier_stats = {}
    
    for col in numeric_columns:
        if df[col].notna().sum() > 0:
            z_scores = np.abs(stats.zscore(df[col].dropna()))
            outlier_count = np.sum(z_scores > 3)
            outlier_percentage = (outlier_count / df[col].notna().sum()) * 100
            
            outlier_stats[col] = {
                'count': outlier_count,
                'percentage': outlier_percentage
            }
            
            if outlier_percentage > thresholds['outlier_percentage_max']:
                quality_report['warnings'].append(
                    f"Column {col}: {outlier_percentage:.1f}% outliers (threshold: {thresholds['outlier_percentage_max']}%)"
                )
    
    quality_report['statistics']['outliers'] = outlier_stats
    
    # 4. Variance analysis
    low_variance_features = []
    for col in numeric_columns:
        if df[col].notna().sum() > 1:
            variance = df[col].var()
            if variance < thresholds['variance_min']:
                low_variance_features.append(col)
    
    if low_variance_features:
        quality_report['warnings'].append(
            f"Low variance features: {low_variance_features}"
        )
        quality_report['recommendations'].append(
            "Consider removing low variance features"
        )
    
    # 5. Correlation analysis
    if len(numeric_columns) > 1:
        corr_matrix = df[numeric_columns].corr()
        high_corr_pairs = []
        
        for i in range(len(corr_matrix.columns)):
            for j in range(i+1, len(corr_matrix.columns)):
                corr_val = abs(corr_matrix.iloc[i, j])
                if corr_val > thresholds['correlation_max']:
                    high_corr_pairs.append(
                        (corr_matrix.columns[i], corr_matrix.columns[j], corr_val)
                    )
        
        if high_corr_pairs:
            quality_report['warnings'].append(
                f"High correlation pairs: {[(pair[0], pair[1], f'{pair[2]:.3f}') for pair in high_corr_pairs]}"
            )
            quality_report['recommendations'].append(
                "Consider removing highly correlated features"
            )
    
    # 6. Calculate overall quality score
    score_components = []
    
    # Missing value score (0-1, higher is better)
    avg_missing_pct = np.mean([stats['percentage'] for stats in missing_stats.values()])
    missing_score = max(0, 1 - (avg_missing_pct / 100))
    score_components.append(missing_score)
    
    # Duplicate score (0-1, higher is better)
    duplicate_score = max(0, 1 - (duplicate_percentage / 100))
    score_components.append(duplicate_score)
    
    # Outlier score (0-1, higher is better)
    if outlier_stats:
        avg_outlier_pct = np.mean([stats['percentage'] for stats in outlier_stats.values()])
        outlier_score = max(0, 1 - (avg_outlier_pct / 100))
        score_components.append(outlier_score)
    
    # Overall quality score
    quality_report['overall_quality_score'] = np.mean(score_components)
    quality_report['is_high_quality'] = quality_report['overall_quality_score'] > 0.8
    
    return quality_report


def check_data_leakage(X: Union[pd.DataFrame, np.ndarray],
                      y: Union[pd.Series, np.ndarray],
                      feature_names: Optional[List[str]] = None,
                      correlation_threshold: float = 0.95,
                      mutual_info_threshold: float = 0.9) -> Dict[str, Any]:
    """
    Check for potential data leakage between features and target.
    
    Args:
        X: Feature data
        y: Target data
        feature_names: Optional feature names
        correlation_threshold: Threshold for correlation-based leakage detection
        mutual_info_threshold: Threshold for mutual information-based leakage detection
        
    Returns:
        Data leakage report dictionary
    """
    leakage_report = {
        'has_leakage': False,
        'suspicious_features': [],
        'correlation_analysis': {},
        'mutual_info_analysis': {},
        'recommendations': []
    }
    
    # Convert to appropriate formats
    if isinstance(X, np.ndarray):
        if feature_names:
            X_df = pd.DataFrame(X.reshape(-1, X.shape[-1]), columns=feature_names)
        else:
            X_df = pd.DataFrame(X.reshape(-1, X.shape[-1]))
            feature_names = [f"feature_{i}" for i in range(X_df.shape[1])]
    else:
        X_df = X.copy()
        feature_names = list(X_df.columns)
    
    if isinstance(y, pd.Series):
        y_array = y.values
    else:
        y_array = np.array(y).flatten()
    
    # 1. Correlation analysis
    for i, feature_name in enumerate(feature_names):
        if feature_name in X_df.columns:
            feature_data = X_df[feature_name].values
            
            # Remove NaN values for correlation calculation
            valid_mask = ~(np.isnan(feature_data) | np.isnan(y_array))
            if np.sum(valid_mask) > 10:  # Need at least 10 valid points
                correlation = np.corrcoef(feature_data[valid_mask], y_array[valid_mask])[0, 1]
                
                leakage_report['correlation_analysis'][feature_name] = {
                    'correlation': correlation,
                    'abs_correlation': abs(correlation),
                    'is_suspicious': abs(correlation) > correlation_threshold
                }
                
                if abs(correlation) > correlation_threshold:
                    leakage_report['suspicious_features'].append(feature_name)
                    leakage_report['has_leakage'] = True
    
    # 2. Mutual information analysis (if sklearn available)
    if SKLEARN_AVAILABLE:
        try:
            # Prepare data for mutual information
            valid_rows = ~(np.isnan(X_df.values).any(axis=1) | np.isnan(y_array))
            if np.sum(valid_rows) > 10:
                X_clean = X_df.values[valid_rows]
                y_clean = y_array[valid_rows]
                
                # Calculate mutual information
                mi_scores = mutual_info_regression(X_clean, y_clean, random_state=42)
                
                # Normalize MI scores to [0, 1] range
                if len(mi_scores) > 0:
                    mi_scores_norm = mi_scores / np.max(mi_scores) if np.max(mi_scores) > 0 else mi_scores
                    
                    for i, feature_name in enumerate(feature_names):
                        if i < len(mi_scores_norm):
                            leakage_report['mutual_info_analysis'][feature_name] = {
                                'mutual_info': mi_scores[i],
                                'mutual_info_normalized': mi_scores_norm[i],
                                'is_suspicious': mi_scores_norm[i] > mutual_info_threshold
                            }
                            
                            if mi_scores_norm[i] > mutual_info_threshold:
                                if feature_name not in leakage_report['suspicious_features']:
                                    leakage_report['suspicious_features'].append(feature_name)
                                    leakage_report['has_leakage'] = True
        
        except Exception as e:
            leakage_report['mutual_info_analysis']['error'] = str(e)
    
    # 3. Generate recommendations
    if leakage_report['has_leakage']:
        leakage_report['recommendations'].extend([
            f"Investigate suspicious features: {leakage_report['suspicious_features']}",
            "Check if features contain future information",
            "Verify temporal ordering of data",
            "Consider removing or transforming suspicious features"
        ])
    else:
        leakage_report['recommendations'].append("No obvious data leakage detected")
    
    return leakage_report


def validate_feature_distributions(data: Union[pd.DataFrame, np.ndarray],
                                  feature_names: Optional[List[str]] = None,
                                  reference_data: Optional[Union[pd.DataFrame, np.ndarray]] = None) -> Dict[str, Any]:
    """
    Validate feature distributions and detect distribution shifts.
    
    Args:
        data: Current data to validate
        feature_names: Optional feature names
        reference_data: Optional reference data for comparison
        
    Returns:
        Distribution validation report
    """
    distribution_report = {
        'feature_distributions': {},
        'distribution_shifts': {},
        'normality_tests': {},
        'recommendations': []
    }
    
    # Convert to DataFrame if needed
    if isinstance(data, np.ndarray):
        if feature_names:
            df = pd.DataFrame(data.reshape(-1, data.shape[-1]), columns=feature_names)
        else:
            df = pd.DataFrame(data.reshape(-1, data.shape[-1]))
            feature_names = [f"feature_{i}" for i in range(df.shape[1])]
    else:
        df = data.copy()
        feature_names = list(df.columns)
    
    # Analyze each feature
    for feature_name in feature_names:
        if feature_name in df.columns:
            feature_data = df[feature_name].dropna()
            
            if len(feature_data) > 0:
                # Basic distribution statistics
                distribution_report['feature_distributions'][feature_name] = {
                    'count': len(feature_data),
                    'mean': feature_data.mean(),
                    'std': feature_data.std(),
                    'min': feature_data.min(),
                    'max': feature_data.max(),
                    'skewness': stats.skew(feature_data),
                    'kurtosis': stats.kurtosis(feature_data),
                    'percentiles': {
                        '25th': feature_data.quantile(0.25),
                        '50th': feature_data.quantile(0.50),
                        '75th': feature_data.quantile(0.75)
                    }
                }
                
                # Normality test
                if len(feature_data) >= 8:  # Minimum for Shapiro-Wilk test
                    try:
                        if len(feature_data) <= 5000:  # Shapiro-Wilk for smaller samples
                            stat, p_value = stats.shapiro(feature_data)
                            test_name = 'shapiro_wilk'
                        else:  # Kolmogorov-Smirnov for larger samples
                            stat, p_value = stats.kstest(feature_data, 'norm')
                            test_name = 'kolmogorov_smirnov'
                        
                        distribution_report['normality_tests'][feature_name] = {
                            'test': test_name,
                            'statistic': stat,
                            'p_value': p_value,
                            'is_normal': p_value > 0.05
                        }
                    except Exception as e:
                        distribution_report['normality_tests'][feature_name] = {
                            'error': str(e)
                        }
    
    # Compare with reference data if provided
    if reference_data is not None:
        if isinstance(reference_data, np.ndarray):
            if feature_names:
                ref_df = pd.DataFrame(reference_data.reshape(-1, reference_data.shape[-1]), columns=feature_names)
            else:
                ref_df = pd.DataFrame(reference_data.reshape(-1, reference_data.shape[-1]))
        else:
            ref_df = reference_data.copy()
        
        for feature_name in feature_names:
            if feature_name in df.columns and feature_name in ref_df.columns:
                current_data = df[feature_name].dropna()
                ref_data = ref_df[feature_name].dropna()
                
                if len(current_data) > 0 and len(ref_data) > 0:
                    try:
                        # Kolmogorov-Smirnov test for distribution comparison
                        ks_stat, ks_p_value = stats.ks_2samp(current_data, ref_data)
                        
                        # Mann-Whitney U test for median comparison
                        mw_stat, mw_p_value = stats.mannwhitneyu(current_data, ref_data, alternative='two-sided')
                        
                        distribution_report['distribution_shifts'][feature_name] = {
                            'ks_test': {
                                'statistic': ks_stat,
                                'p_value': ks_p_value,
                                'significant_shift': ks_p_value < 0.05
                            },
                            'mann_whitney_test': {
                                'statistic': mw_stat,
                                'p_value': mw_p_value,
                                'significant_shift': mw_p_value < 0.05
                            },
                            'mean_shift': current_data.mean() - ref_data.mean(),
                            'std_ratio': current_data.std() / ref_data.std() if ref_data.std() > 0 else np.inf
                        }
                        
                        # Flag significant shifts
                        if ks_p_value < 0.05 or mw_p_value < 0.05:
                            distribution_report['recommendations'].append(
                                f"Significant distribution shift detected in {feature_name}"
                            )
                    
                    except Exception as e:
                        distribution_report['distribution_shifts'][feature_name] = {
                            'error': str(e)
                        }
    
    # Generate general recommendations
    non_normal_features = [
        name for name, test in distribution_report['normality_tests'].items()
        if 'is_normal' in test and not test['is_normal']
    ]
    
    if non_normal_features:
        distribution_report['recommendations'].append(
            f"Non-normal distributions detected: {non_normal_features}. Consider transformation."
        )
    
    return distribution_report


def validate_temporal_consistency(data: pd.DataFrame,
                                time_column: str,
                                value_columns: List[str],
                                well_column: Optional[str] = None) -> Dict[str, Any]:
    """
    Validate temporal consistency in well log data.
    
    Args:
        data: DataFrame with temporal data
        time_column: Name of time/depth column
        value_columns: List of value columns to check
        well_column: Optional well identifier column
        
    Returns:
        Temporal consistency report
    """
    temporal_report = {
        'is_temporally_consistent': True,
        'issues': [],
        'statistics': {},
        'recommendations': []
    }
    
    # Check if time column exists
    if time_column not in data.columns:
        temporal_report['is_temporally_consistent'] = False
        temporal_report['issues'].append(f"Time column '{time_column}' not found")
        return temporal_report
    
    # Group by well if well column is provided
    if well_column and well_column in data.columns:
        groups = data.groupby(well_column)
    else:
        groups = [(None, data)]
    
    for group_name, group_data in groups:
        group_prefix = f"Well {group_name}: " if group_name else ""
        
        # Check for monotonic time ordering
        time_values = group_data[time_column].values
        if not np.all(np.diff(time_values) >= 0):
            temporal_report['is_temporally_consistent'] = False
            temporal_report['issues'].append(f"{group_prefix}Non-monotonic time ordering")
        
        # Check for duplicate time values
        duplicate_times = group_data[time_column].duplicated().sum()
        if duplicate_times > 0:
            temporal_report['issues'].append(f"{group_prefix}{duplicate_times} duplicate time values")
        
        # Check for large gaps in time series
        time_diffs = np.diff(time_values)
        if len(time_diffs) > 0:
            median_diff = np.median(time_diffs)
            large_gaps = np.sum(time_diffs > 5 * median_diff)
            if large_gaps > 0:
                temporal_report['issues'].append(f"{group_prefix}{large_gaps} large time gaps detected")
        
        # Statistics for this group
        group_stats = {
            'time_range': (time_values.min(), time_values.max()),
            'n_points': len(time_values),
            'median_time_diff': np.median(time_diffs) if len(time_diffs) > 0 else 0,
            'duplicate_times': duplicate_times
        }
        
        if group_name:
            temporal_report['statistics'][group_name] = group_stats
        else:
            temporal_report['statistics']['overall'] = group_stats
    
    # Generate recommendations
    if not temporal_report['is_temporally_consistent']:
        temporal_report['recommendations'].extend([
            "Sort data by time/depth column",
            "Remove or interpolate duplicate time values",
            "Consider interpolation for large gaps"
        ])
    
    return temporal_report


# Export public interface
__all__ = [
    'validate_data_quality',
    'check_data_leakage',
    'validate_feature_distributions',
    'validate_temporal_consistency',
    'SKLEARN_AVAILABLE'
]
