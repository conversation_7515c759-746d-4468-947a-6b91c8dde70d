"""
Core ML Algorithms Package

This package contains the core machine learning algorithms used throughout the system:
- regression.py: Multiple Linear Regression utilities and wrappers
- metrics.py: Evaluation metrics for regression and imputation tasks
- preprocessing.py: Advanced data preprocessing and stabilization utilities

These algorithms are consolidated from various locations in the original codebase
to provide a centralized, well-organized ML algorithms library.
"""

# Import core algorithms with safe fallbacks
try:
    from .regression import MLRModelWrapper, create_mlr_model, validate_mlr_assumptions
    REGRESSION_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Regression utilities not available: {e}")
    MLRModelWrapper = None
    create_mlr_model = None
    validate_mlr_assumptions = None
    REGRESSION_AVAILABLE = False

try:
    from .metrics import cal_r2, cal_cc, evaluate_model_comprehensive
    METRICS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Metrics utilities not available: {e}")
    cal_r2 = None
    cal_cc = None
    evaluate_model_comprehensive = None
    METRICS_AVAILABLE = False

try:
    from .preprocessing import (
        phase1_preprocessing_pipeline,
        enhanced_validate_sequences,
        get_recommended_preprocessing_config
    )
    PREPROCESSING_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Advanced preprocessing not available: {e}")
    phase1_preprocessing_pipeline = None
    enhanced_validate_sequences = None
    get_recommended_preprocessing_config = None
    PREPROCESSING_AVAILABLE = False

# Export public interface
__all__ = [
    # Regression
    'MLRModelWrapper',
    'create_mlr_model',
    'validate_mlr_assumptions',
    
    # Metrics
    'cal_r2',
    'cal_cc',
    'evaluate_model_comprehensive',
    
    # Preprocessing
    'phase1_preprocessing_pipeline',
    'enhanced_validate_sequences',
    'get_recommended_preprocessing_config',
    
    # Status flags
    'REGRESSION_AVAILABLE',
    'METRICS_AVAILABLE',
    'PREPROCESSING_AVAILABLE'
]
