"""
Multiple Linear Regression Utilities

Consolidated MLR utilities for well log imputation, moved from utils/mlr_utils.py
for better organization within the ML algorithms package.

This module provides specialized utilities for implementing multiple linear regression (MLR)
in well log imputation workflows. It includes preprocessing, diagnostic tools, and model
wrappers that integrate seamlessly with the existing ML pipeline.

Key Features:
- Automated preprocessing (scaling, outlier detection)
- Multicollinearity detection using Variance Inflation Factor (VIF)
- Linear regression assumption validation
- Diagnostic plotting and statistical analysis
- Robust error handling with graceful fallbacks
"""

import numpy as np
import pandas as pd
import warnings
from typing import Dict, List, Tuple, Optional, Union, Any
from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.base import BaseEstimator, RegressorMixin
import scipy.stats as stats

# Optional dependencies with graceful fallbacks
try:
    from statsmodels.stats.outliers_influence import variance_inflation_factor
    STATSMODELS_AVAILABLE = True
except ImportError:
    STATSMODELS_AVAILABLE = False
    warnings.warn("Statsmodels not available. VIF calculation will be skipped.")

try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    PLOTTING_AVAILABLE = True
except ImportError:
    PLOTTING_AVAILABLE = False
    warnings.warn("Matplotlib/Seaborn not available. Diagnostic plots will be skipped.")


class MLRPreprocessor:
    """
    Preprocessor for Multiple Linear Regression models.
    
    Handles feature scaling, outlier detection, and multicollinearity analysis
    specifically designed for well log data characteristics.
    """
    
    def __init__(self, 
                 scaling_method: str = 'standard',
                 outlier_threshold: float = 3.0,
                 vif_threshold: float = 10.0,
                 handle_outliers: bool = True,
                 enable_diagnostics: bool = False):
        """
        Initialize MLR preprocessor.
        
        Args:
            scaling_method: Method for feature scaling ('standard', 'robust', 'minmax')
            outlier_threshold: Z-score threshold for outlier detection
            vif_threshold: VIF threshold for multicollinearity detection
            handle_outliers: Whether to remove/cap outliers
            enable_diagnostics: Whether to generate diagnostic output
        """
        self.scaling_method = scaling_method
        self.outlier_threshold = outlier_threshold
        self.vif_threshold = vif_threshold
        self.handle_outliers = handle_outliers
        self.enable_diagnostics = enable_diagnostics
        
        # Initialize scaler based on method
        if scaling_method == 'standard':
            self.scaler = StandardScaler()
        elif scaling_method == 'robust':
            self.scaler = RobustScaler()
        elif scaling_method == 'minmax':
            self.scaler = MinMaxScaler()
        else:
            raise ValueError(f"Unknown scaling method: {scaling_method}")
        
        self.is_fitted = False
        self.feature_names = None
        self.outlier_mask = None
        self.vif_values = None
        self.diagnostics = {}
    
    def fit(self, X: pd.DataFrame, y: pd.Series = None) -> 'MLRPreprocessor':
        """
        Fit the preprocessor to the training data.
        
        Args:
            X: Feature matrix
            y: Target vector (optional, for diagnostics)
            
        Returns:
            Self for method chaining
        """
        X = pd.DataFrame(X) if not isinstance(X, pd.DataFrame) else X
        self.feature_names = list(X.columns)
        
        # Handle outliers if requested
        if self.handle_outliers:
            self.outlier_mask = self._detect_outliers(X)
            X_clean = X[~self.outlier_mask]
            if self.enable_diagnostics:
                outlier_count = self.outlier_mask.sum()
                print(f"🔍 MLR Preprocessing: Detected {outlier_count} outliers ({outlier_count/len(X)*100:.1f}%)")
        else:
            X_clean = X
            self.outlier_mask = pd.Series(False, index=X.index)
        
        # Fit scaler
        self.scaler.fit(X_clean)
        
        # Calculate VIF if available
        if STATSMODELS_AVAILABLE and len(X_clean.columns) > 1:
            try:
                X_scaled = pd.DataFrame(
                    self.scaler.transform(X_clean),
                    columns=X_clean.columns,
                    index=X_clean.index
                )
                self.vif_values = self._calculate_vif(X_scaled)
                
                if self.enable_diagnostics:
                    self._print_vif_diagnostics()
                    
            except Exception as e:
                if self.enable_diagnostics:
                    print(f"⚠️ VIF calculation failed: {e}")
                self.vif_values = None
        
        self.is_fitted = True
        return self
    
    def transform(self, X: pd.DataFrame) -> pd.DataFrame:
        """
        Transform features using fitted preprocessor.
        
        Args:
            X: Feature matrix to transform
            
        Returns:
            Transformed feature matrix
        """
        if not self.is_fitted:
            raise ValueError("Preprocessor must be fitted before transform")
        
        X = pd.DataFrame(X) if not isinstance(X, pd.DataFrame) else X
        
        # Apply scaling
        X_scaled = pd.DataFrame(
            self.scaler.transform(X),
            columns=X.columns,
            index=X.index
        )
        
        return X_scaled
    
    def fit_transform(self, X: pd.DataFrame, y: pd.Series = None) -> pd.DataFrame:
        """
        Fit preprocessor and transform data in one step.
        
        Args:
            X: Feature matrix
            y: Target vector (optional)
            
        Returns:
            Transformed feature matrix
        """
        return self.fit(X, y).transform(X)
    
    def _detect_outliers(self, X: pd.DataFrame) -> pd.Series:
        """
        Detect outliers using Z-score method.
        
        Args:
            X: Feature matrix
            
        Returns:
            Boolean mask indicating outliers
        """
        z_scores = np.abs(stats.zscore(X, nan_policy='omit'))
        outlier_mask = (z_scores > self.outlier_threshold).any(axis=1)
        return outlier_mask
    
    def _calculate_vif(self, X: pd.DataFrame) -> Dict[str, float]:
        """
        Calculate Variance Inflation Factor for each feature.
        
        Args:
            X: Scaled feature matrix
            
        Returns:
            Dictionary mapping feature names to VIF values
        """
        vif_data = {}
        for i, feature in enumerate(X.columns):
            try:
                vif_value = variance_inflation_factor(X.values, i)
                vif_data[feature] = vif_value
            except Exception as e:
                if self.enable_diagnostics:
                    print(f"⚠️ VIF calculation failed for {feature}: {e}")
                vif_data[feature] = np.nan
        
        return vif_data
    
    def _print_vif_diagnostics(self):
        """Print VIF diagnostic information."""
        if self.vif_values is None:
            return
        
        print("\n🔍 Multicollinearity Analysis (VIF):")
        print("-" * 40)
        
        high_vif_features = []
        for feature, vif in self.vif_values.items():
            if not np.isnan(vif):
                status = "⚠️ HIGH" if vif > self.vif_threshold else "✅ OK"
                print(f"  {feature:15s}: {vif:6.2f} {status}")
                
                if vif > self.vif_threshold:
                    high_vif_features.append(feature)
        
        if high_vif_features:
            print(f"\n⚠️ Features with high multicollinearity: {', '.join(high_vif_features)}")
            print("   Consider removing or combining these features.")


class MLRModelWrapper(BaseEstimator, RegressorMixin):
    """
    Wrapper for Multiple Linear Regression models with preprocessing and diagnostics.
    
    This wrapper provides a unified interface for different regression models
    (Linear, Ridge, Lasso, ElasticNet) with automatic preprocessing and validation.
    """
    
    def __init__(self, 
                 model_type: str = 'linear',
                 scaling_method: str = 'standard',
                 outlier_threshold: float = 3.0,
                 vif_threshold: float = 10.0,
                 handle_outliers: bool = True,
                 enable_diagnostics: bool = False,
                 **model_kwargs):
        """
        Initialize MLR model wrapper.
        
        Args:
            model_type: Type of regression model ('linear', 'ridge', 'lasso', 'elastic_net')
            scaling_method: Feature scaling method
            outlier_threshold: Z-score threshold for outlier detection
            vif_threshold: VIF threshold for multicollinearity detection
            handle_outliers: Whether to handle outliers
            enable_diagnostics: Whether to enable diagnostic output
            **model_kwargs: Additional arguments for the underlying model
        """
        self.model_type = model_type
        self.model_kwargs = model_kwargs
        
        # Initialize preprocessor
        self.preprocessor = MLRPreprocessor(
            scaling_method=scaling_method,
            outlier_threshold=outlier_threshold,
            vif_threshold=vif_threshold,
            handle_outliers=handle_outliers,
            enable_diagnostics=enable_diagnostics
        )
        
        # Initialize model
        self.model = self._create_model()
        self.is_fitted = False
        self.feature_names = None
        self.diagnostics = {}
    
    def _create_model(self):
        """Create the underlying regression model."""
        model_classes = {
            'linear': LinearRegression,
            'ridge': Ridge,
            'lasso': Lasso,
            'elastic_net': ElasticNet
        }
        
        if self.model_type not in model_classes:
            raise ValueError(f"Unknown model type: {self.model_type}")
        
        model_class = model_classes[self.model_type]
        return model_class(**self.model_kwargs)
    
    def fit(self, X, y):
        """
        Fit the MLR model with preprocessing.
        
        Args:
            X: Feature matrix
            y: Target vector
            
        Returns:
            Self for method chaining
        """
        X = pd.DataFrame(X) if not isinstance(X, pd.DataFrame) else X
        y = pd.Series(y) if not isinstance(y, pd.Series) else y
        
        self.feature_names = list(X.columns)
        
        # Preprocess features
        X_processed = self.preprocessor.fit_transform(X, y)
        
        # Remove outliers from both X and y if requested
        if self.preprocessor.handle_outliers and self.preprocessor.outlier_mask is not None:
            clean_mask = ~self.preprocessor.outlier_mask
            X_processed = X_processed[clean_mask]
            y_clean = y[clean_mask]
        else:
            y_clean = y
        
        # Fit model
        self.model.fit(X_processed, y_clean)
        self.is_fitted = True
        
        return self
    
    def predict(self, X):
        """
        Make predictions using the fitted model.
        
        Args:
            X: Feature matrix
            
        Returns:
            Predictions
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
        
        X = pd.DataFrame(X) if not isinstance(X, pd.DataFrame) else X
        X_processed = self.preprocessor.transform(X)
        
        return self.model.predict(X_processed)
    
    def score(self, X, y):
        """
        Calculate R² score.
        
        Args:
            X: Feature matrix
            y: Target vector
            
        Returns:
            R² score
        """
        y_pred = self.predict(X)
        return r2_score(y, y_pred)


def create_mlr_model(model_type: str = 'linear', **kwargs) -> MLRModelWrapper:
    """
    Factory function to create MLR model instances.
    
    Args:
        model_type: Type of regression model
        **kwargs: Additional arguments for the model
        
    Returns:
        MLRModelWrapper instance
    """
    return MLRModelWrapper(model_type=model_type, **kwargs)


def validate_mlr_assumptions(X: pd.DataFrame, y: pd.Series, 
                           model: MLRModelWrapper = None) -> Dict[str, Any]:
    """
    Validate linear regression assumptions.
    
    Args:
        X: Feature matrix
        y: Target vector
        model: Fitted MLR model (optional)
        
    Returns:
        Dictionary with assumption validation results
    """
    results = {
        'linearity': {'status': 'unknown', 'details': 'Not implemented'},
        'independence': {'status': 'assumed', 'details': 'Assumed for well log data'},
        'homoscedasticity': {'status': 'unknown', 'details': 'Not implemented'},
        'normality': {'status': 'unknown', 'details': 'Not implemented'},
        'no_multicollinearity': {'status': 'unknown', 'details': 'Check VIF values'}
    }
    
    # Check multicollinearity if model is provided
    if model and hasattr(model.preprocessor, 'vif_values') and model.preprocessor.vif_values:
        max_vif = max(v for v in model.preprocessor.vif_values.values() if not np.isnan(v))
        if max_vif > model.preprocessor.vif_threshold:
            results['no_multicollinearity'] = {
                'status': 'violated',
                'details': f'Max VIF: {max_vif:.2f} > {model.preprocessor.vif_threshold}'
            }
        else:
            results['no_multicollinearity'] = {
                'status': 'satisfied',
                'details': f'Max VIF: {max_vif:.2f} <= {model.preprocessor.vif_threshold}'
            }
    
    return results


# Export public interface
__all__ = [
    'MLRPreprocessor',
    'MLRModelWrapper',
    'create_mlr_model',
    'validate_mlr_assumptions',
    'STATSMODELS_AVAILABLE',
    'PLOTTING_AVAILABLE'
]
