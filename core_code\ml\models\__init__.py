"""
ML Models Package

This package provides unified interfaces for both shallow and deep learning models
used in the ML Log Prediction system.

Structure:
- shallow.py: Shallow ML models (XGBoost, LightGBM, CatBoost, etc.)
- deep.py: Deep learning models (Neural Networks, Transformers, etc.)

These interfaces provide consistent APIs for model creation, training, and inference
across different model types.
"""

# Import model interfaces with safe fallbacks
try:
    from .shallow import (
        get_shallow_models,
        create_shallow_model,
        ShallowModelWrapper,
        SHALLOW_MODELS_AVAILABLE
    )
    SHALLOW_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Shallow models not available: {e}")
    get_shallow_models = None
    create_shallow_model = None
    ShallowModelWrapper = None
    SHALLOW_MODELS_AVAILABLE = False
    SHALLOW_AVAILABLE = False

try:
    from .deep import (
        get_deep_models,
        create_deep_model,
        DeepModelWrapper,
        DEEP_MODELS_AVAILABLE
    )
    DEEP_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Deep models not available: {e}")
    get_deep_models = None
    create_deep_model = None
    DeepModelWrapper = None
    DEEP_MODELS_AVAILABLE = False
    DEEP_AVAILABLE = False

# Export public interface
__all__ = [
    # Shallow models
    'get_shallow_models',
    'create_shallow_model',
    'ShallowModelWrapper',
    
    # Deep models
    'get_deep_models',
    'create_deep_model',
    'DeepModelWrapper',
    
    # Status flags
    'SHALLOW_AVAILABLE',
    'DEEP_AVAILABLE',
    'SHALLOW_MODELS_AVAILABLE',
    'DEEP_MODELS_AVAILABLE'
]

print("ML models package initialized")
