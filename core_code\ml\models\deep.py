"""
Deep Learning Models Interface

This module provides unified interfaces for deep learning models including
neural networks, transformers, and specialized time series models.

Key Features:
- Unified model interface for different deep learning frameworks
- GPU acceleration support
- Model comparison and selection utilities
- Integration with existing advanced models (SAITS, BRITS)

Author: ML Models Core
"""

import warnings
from typing import Dict, List, Tuple, Optional, Union, Any
import numpy as np

# Optional dependencies with graceful fallbacks
try:
    import torch
    import torch.nn as nn
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    warnings.warn("PyTorch not available.")

try:
    import tensorflow as tf
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    warnings.warn("TensorFlow not available.")

# Check for existing advanced models
try:
    from models.advanced_models.saits_model import SAITSModel
    SAITS_AVAILABLE = True
except ImportError:
    SAITS_AVAILABLE = False

try:
    from models.advanced_models.brits_model import BRITSModel
    BRITS_AVAILABLE = True
except ImportError:
    BRITS_AVAILABLE = False

try:
    from models.neuralnet import SimpleNeuralNet
    SIMPLE_NN_AVAILABLE = True
except ImportError:
    SIMPLE_NN_AVAILABLE = False

# Check overall availability
DEEP_MODELS_AVAILABLE = any([TORCH_AVAILABLE, TENSORFLOW_AVAILABLE, SAITS_AVAILABLE, BRITS_AVAILABLE])


class DeepModelWrapper:
    """Unified wrapper for deep learning models."""
    
    def __init__(self, 
                 model_type: str,
                 model_params: Optional[Dict[str, Any]] = None,
                 use_gpu: bool = True):
        """
        Initialize deep model wrapper.
        
        Args:
            model_type: Type of model ('simple_nn', 'saits', 'brits', 'transformer', etc.)
            model_params: Model-specific parameters
            use_gpu: Whether to use GPU acceleration if available
        """
        self.model_type = model_type
        self.model_params = model_params or {}
        self.use_gpu = use_gpu and TORCH_AVAILABLE and torch.cuda.is_available()
        self.model = None
        self.is_fitted = False
        self.device = torch.device('cuda' if self.use_gpu else 'cpu') if TORCH_AVAILABLE else None
        
        # Create model
        self.model = self._create_model()
        
        # Move to GPU if available
        if self.model and self.use_gpu and hasattr(self.model, 'to'):
            self.model = self.model.to(self.device)
    
    def _create_model(self):
        """Create the underlying model based on model_type."""
        if self.model_type == 'simple_nn':
            if not TORCH_AVAILABLE:
                raise ImportError("PyTorch not available")
            
            # Default parameters for simple neural network
            default_params = {
                'input_size': 64,
                'hidden_sizes': [128, 64, 32],
                'output_size': 1,
                'dropout': 0.1,
                'activation': 'relu'
            }
            
            params = {**default_params, **self.model_params}
            return self._create_simple_nn(**params)
        
        elif self.model_type == 'saits':
            if not SAITS_AVAILABLE:
                raise ImportError("SAITS model not available")
            
            default_params = {
                'n_steps': 64,
                'n_features': 4,
                'd_model': 64,
                'n_heads': 8,
                'n_layers': 3,
                'd_ffn': 128,
                'dropout': 0.1
            }
            
            params = {**default_params, **self.model_params}
            return SAITSModel(**params)
        
        elif self.model_type == 'brits':
            if not BRITS_AVAILABLE:
                raise ImportError("BRITS model not available")
            
            default_params = {
                'n_steps': 64,
                'n_features': 4,
                'rnn_hidden_size': 64,
                'dropout': 0.1
            }
            
            params = {**default_params, **self.model_params}
            return BRITSModel(**params)
        
        elif self.model_type == 'lstm':
            if not TORCH_AVAILABLE:
                raise ImportError("PyTorch not available")
            
            default_params = {
                'input_size': 4,
                'hidden_size': 64,
                'num_layers': 2,
                'output_size': 1,
                'dropout': 0.1,
                'bidirectional': False
            }
            
            params = {**default_params, **self.model_params}
            return self._create_lstm(**params)
        
        elif self.model_type == 'gru':
            if not TORCH_AVAILABLE:
                raise ImportError("PyTorch not available")
            
            default_params = {
                'input_size': 4,
                'hidden_size': 64,
                'num_layers': 2,
                'output_size': 1,
                'dropout': 0.1,
                'bidirectional': False
            }
            
            params = {**default_params, **self.model_params}
            return self._create_gru(**params)
        
        else:
            raise ValueError(f"Unknown model type: {self.model_type}")
    
    def _create_simple_nn(self, input_size, hidden_sizes, output_size, dropout, activation):
        """Create a simple feedforward neural network."""
        layers = []
        
        # Input layer
        layers.append(nn.Linear(input_size, hidden_sizes[0]))
        layers.append(self._get_activation(activation))
        layers.append(nn.Dropout(dropout))
        
        # Hidden layers
        for i in range(len(hidden_sizes) - 1):
            layers.append(nn.Linear(hidden_sizes[i], hidden_sizes[i + 1]))
            layers.append(self._get_activation(activation))
            layers.append(nn.Dropout(dropout))
        
        # Output layer
        layers.append(nn.Linear(hidden_sizes[-1], output_size))
        
        return nn.Sequential(*layers)
    
    def _create_lstm(self, input_size, hidden_size, num_layers, output_size, dropout, bidirectional):
        """Create an LSTM model."""
        class LSTMModel(nn.Module):
            def __init__(self, input_size, hidden_size, num_layers, output_size, dropout, bidirectional):
                super().__init__()
                self.lstm = nn.LSTM(
                    input_size=input_size,
                    hidden_size=hidden_size,
                    num_layers=num_layers,
                    dropout=dropout if num_layers > 1 else 0,
                    bidirectional=bidirectional,
                    batch_first=True
                )
                
                lstm_output_size = hidden_size * (2 if bidirectional else 1)
                self.fc = nn.Linear(lstm_output_size, output_size)
                self.dropout = nn.Dropout(dropout)
            
            def forward(self, x):
                lstm_out, _ = self.lstm(x)
                # Use the last time step output
                last_output = lstm_out[:, -1, :]
                output = self.dropout(last_output)
                output = self.fc(output)
                return output
        
        return LSTMModel(input_size, hidden_size, num_layers, output_size, dropout, bidirectional)
    
    def _create_gru(self, input_size, hidden_size, num_layers, output_size, dropout, bidirectional):
        """Create a GRU model."""
        class GRUModel(nn.Module):
            def __init__(self, input_size, hidden_size, num_layers, output_size, dropout, bidirectional):
                super().__init__()
                self.gru = nn.GRU(
                    input_size=input_size,
                    hidden_size=hidden_size,
                    num_layers=num_layers,
                    dropout=dropout if num_layers > 1 else 0,
                    bidirectional=bidirectional,
                    batch_first=True
                )
                
                gru_output_size = hidden_size * (2 if bidirectional else 1)
                self.fc = nn.Linear(gru_output_size, output_size)
                self.dropout = nn.Dropout(dropout)
            
            def forward(self, x):
                gru_out, _ = self.gru(x)
                # Use the last time step output
                last_output = gru_out[:, -1, :]
                output = self.dropout(last_output)
                output = self.fc(output)
                return output
        
        return GRUModel(input_size, hidden_size, num_layers, output_size, dropout, bidirectional)
    
    def _get_activation(self, activation):
        """Get activation function."""
        if activation == 'relu':
            return nn.ReLU()
        elif activation == 'tanh':
            return nn.Tanh()
        elif activation == 'sigmoid':
            return nn.Sigmoid()
        elif activation == 'gelu':
            return nn.GELU()
        else:
            return nn.ReLU()  # Default
    
    def fit(self, X, y, validation_data=None, epochs=100, batch_size=32, learning_rate=0.001, **kwargs):
        """
        Fit the model to training data.
        
        Args:
            X: Training features
            y: Training targets
            validation_data: Optional validation data tuple (X_val, y_val)
            epochs: Number of training epochs
            batch_size: Batch size for training
            learning_rate: Learning rate for optimizer
            **kwargs: Additional training parameters
        """
        if not TORCH_AVAILABLE:
            raise ImportError("PyTorch not available for training")
        
        # Convert to tensors
        X_tensor = torch.FloatTensor(X)
        y_tensor = torch.FloatTensor(y)
        
        if self.use_gpu:
            X_tensor = X_tensor.to(self.device)
            y_tensor = y_tensor.to(self.device)
        
        # Create data loader
        dataset = torch.utils.data.TensorDataset(X_tensor, y_tensor)
        dataloader = torch.utils.data.DataLoader(dataset, batch_size=batch_size, shuffle=True)
        
        # Setup optimizer and loss
        optimizer = torch.optim.Adam(self.model.parameters(), lr=learning_rate)
        criterion = nn.MSELoss()
        
        # Training loop
        self.model.train()
        for epoch in range(epochs):
            total_loss = 0
            for batch_X, batch_y in dataloader:
                optimizer.zero_grad()
                
                # Forward pass
                outputs = self.model(batch_X)
                loss = criterion(outputs.squeeze(), batch_y)
                
                # Backward pass
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
            
            if epoch % 10 == 0:
                avg_loss = total_loss / len(dataloader)
                print(f"Epoch {epoch}/{epochs}, Loss: {avg_loss:.6f}")
        
        self.is_fitted = True
        return self
    
    def predict(self, X):
        """
        Make predictions.
        
        Args:
            X: Features for prediction
            
        Returns:
            Predictions
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
        
        if not TORCH_AVAILABLE:
            raise ImportError("PyTorch not available for prediction")
        
        self.model.eval()
        with torch.no_grad():
            X_tensor = torch.FloatTensor(X)
            if self.use_gpu:
                X_tensor = X_tensor.to(self.device)
            
            predictions = self.model(X_tensor)
            
            # Move back to CPU for numpy conversion
            if self.use_gpu:
                predictions = predictions.cpu()
            
            return predictions.numpy().squeeze()
    
    def score(self, X, y):
        """
        Calculate R² score.
        
        Args:
            X: Features
            y: True targets
            
        Returns:
            R² score
        """
        y_pred = self.predict(X)
        
        # Calculate R²
        ss_res = np.sum((y - y_pred) ** 2)
        ss_tot = np.sum((y - np.mean(y)) ** 2)
        r2 = 1 - (ss_res / ss_tot)
        
        return r2


def get_deep_models() -> List[str]:
    """
    Get list of available deep model types.
    
    Returns:
        List of available model type names
    """
    available_models = []
    
    if TORCH_AVAILABLE:
        available_models.extend(['simple_nn', 'lstm', 'gru'])
    
    if SAITS_AVAILABLE:
        available_models.append('saits')
    
    if BRITS_AVAILABLE:
        available_models.append('brits')
    
    return available_models


def create_deep_model(model_type: str,
                     model_params: Optional[Dict[str, Any]] = None,
                     use_gpu: bool = True) -> DeepModelWrapper:
    """
    Factory function to create deep model instances.
    
    Args:
        model_type: Type of model to create
        model_params: Model-specific parameters
        use_gpu: Whether to use GPU acceleration
        
    Returns:
        DeepModelWrapper instance
    """
    return DeepModelWrapper(model_type, model_params, use_gpu)


def get_model_recommendations(data_shape: Tuple[int, int, int],
                            task_type: str = 'imputation') -> List[str]:
    """
    Get model recommendations based on data characteristics.
    
    Args:
        data_shape: Shape of the dataset (n_samples, seq_len, n_features)
        task_type: Type of task ('imputation', 'prediction', 'classification')
        
    Returns:
        List of recommended model types
    """
    n_samples, seq_len, n_features = data_shape
    recommendations = []
    
    # For time series imputation
    if task_type == 'imputation':
        if SAITS_AVAILABLE:
            recommendations.append('saits')
        if BRITS_AVAILABLE:
            recommendations.append('brits')
    
    # For sequence prediction
    if task_type == 'prediction':
        if TORCH_AVAILABLE:
            recommendations.extend(['lstm', 'gru'])
    
    # For short sequences
    if seq_len < 32:
        if TORCH_AVAILABLE:
            recommendations.append('simple_nn')
    
    # For long sequences
    if seq_len > 128:
        if SAITS_AVAILABLE:
            recommendations.append('saits')  # Transformer-based, good for long sequences
    
    return recommendations


# Export public interface
__all__ = [
    'DeepModelWrapper',
    'get_deep_models',
    'create_deep_model',
    'get_model_recommendations',
    'DEEP_MODELS_AVAILABLE',
    'TORCH_AVAILABLE',
    'TENSORFLOW_AVAILABLE',
    'SAITS_AVAILABLE',
    'BRITS_AVAILABLE'
]
