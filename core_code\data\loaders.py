"""
LAS File Loading and Data Ingestion

This module provides comprehensive LAS file loading and data ingestion functionality.
Moved from utils/data_handler.py for better organization.

Key Features:
- LAS file loading from directories or file lists
- Data cleaning and validation
- Well log data preprocessing
- Results writing back to LAS format

Author: Data Processing Core
"""

import os
import glob
import copy
import warnings
from typing import Dict, List, Tuple, Optional, Union, Any
import pandas as pd
import numpy as np

# Optional dependencies with graceful fallbacks
try:
    import lasio
    LASIO_AVAILABLE = True
except ImportError:
    LASIO_AVAILABLE = False
    warnings.warn("lasio not available. LAS file operations will be disabled.")

try:
    from sklearn.preprocessing import StandardScaler
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    warnings.warn("scikit-learn not available. Some preprocessing features will be disabled.")


def load_las_files_from_directory(input_source: Union[str, List[str]]) -> Tuple[pd.DataFrame, Dict[str, Any], List[str], List[str]]:
    """
    Load LAS files from a directory or list of file paths into a DataFrame.
    
    Args:
        input_source: Directory path (string) or list of file paths
        
    Returns:
        Tuple of (combined_dataframe, las_objects_dict, well_names, log_names)
    """
    if not LASIO_AVAILABLE:
        print("❌ lasio not available. Cannot load LAS files.")
        return pd.DataFrame(), {}, [], []
    
    # Handle both directory path (string) and list of file paths
    if isinstance(input_source, str):
        # Input is a directory path
        las_files = glob.glob(os.path.join(input_source, '*.las'))
        if not las_files:
            print("No LAS files found in directory.")
            return pd.DataFrame(), {}, [], []
    elif isinstance(input_source, list):
        # Input is a list of file paths
        las_files = input_source
        if not las_files:
            print("No LAS files provided.")
            return pd.DataFrame(), {}, [], []
    else:
        print("Invalid input source. Must be directory path or list of file paths.")
        return pd.DataFrame(), {}, [], []

    frames, las_objs, wells = [], {}, []
    
    for f in las_files:
        try:
            las = lasio.read(f)
            well = las.well.WELL.value or os.path.splitext(os.path.basename(f))[0]

            # Convert LAS to DataFrame - handle different lasio versions
            if hasattr(las, 'to_df') and callable(las.to_df):
                df = las.to_df().reset_index()
            elif hasattr(las, 'df') and callable(las.df):
                df = las.df().reset_index()
            else:
                # Fallback - try to access df as property
                df = las.df.reset_index()

            # Find depth column
            depth_col = next((c for c in ['DEPT','DEPTH','MD'] if c in df.columns), None)
            if depth_col is None:
                print(f"{f}: no depth column found.")
                continue
                
            df.rename(columns={depth_col:'MD'}, inplace=True)
            df['WELL'] = well
            frames.append(df)
            las_objs[well] = las
            wells.append(well)
            print(f"✅ Loaded {well}")
            
        except Exception as e:
            print(f"❌ Error loading {f}: {e}")

    if not frames:
        return pd.DataFrame(), {}, [], []

    # Combine all dataframes
    combined_df = pd.concat(frames, ignore_index=True)
    log_names = [c for c in combined_df.columns if c not in ['MD','WELL']]
    
    return combined_df, las_objs, sorted(set(wells)), sorted(log_names)


def load_las_files(file_paths: List[str]) -> Tuple[pd.DataFrame, Dict[str, Any], List[str], List[str]]:
    """
    Load LAS files from a list of file paths.
    
    Args:
        file_paths: List of LAS file paths
        
    Returns:
        Tuple of (combined_dataframe, las_objects_dict, well_names, log_names)
    """
    return load_las_files_from_directory(file_paths)


def clean_log_data(df: pd.DataFrame, 
                  custom_rules: Optional[Dict[str, Tuple[float, float]]] = None) -> pd.DataFrame:
    """
    Clean well log data by applying range-based filtering.
    
    Args:
        df: Input dataframe with well log data
        custom_rules: Optional custom cleaning rules {column: (min, max)}
        
    Returns:
        Cleaned dataframe
    """
    # Default cleaning rules for common well log types
    default_rules = {
        'GR': (0, 300),      # Gamma Ray (API units)
        'NPHI': (0, 1),      # Neutron Porosity (fraction)
        'RHOB': (1.5, 3.0),  # Bulk Density (g/cm3)
        'DT': (40, 200),     # Delta Time (us/ft)
        'RT': (0.1, 1000),   # Resistivity (ohm-m)
        'SP': (-200, 200),   # Spontaneous Potential (mV)
        'CALI': (6, 20),     # Caliper (inches)
        'PEF': (0, 10),      # Photoelectric Factor
    }
    
    # Use custom rules if provided, otherwise use defaults
    rules = custom_rules if custom_rules is not None else default_rules
    
    cleaned_df = df.copy()
    cleaning_report = {'columns_cleaned': [], 'values_removed': 0}
    
    for col, (min_val, max_val) in rules.items():
        if col in cleaned_df.columns:
            # Count values that will be removed
            out_of_range_mask = (cleaned_df[col] < min_val) | (cleaned_df[col] > max_val)
            values_removed = out_of_range_mask.sum()
            
            if values_removed > 0:
                # Set out-of-range values to NaN
                cleaned_df[col] = np.where(
                    (cleaned_df[col] >= min_val) & (cleaned_df[col] <= max_val),
                    cleaned_df[col], 
                    np.nan
                )
                cleaning_report['columns_cleaned'].append(col)
                cleaning_report['values_removed'] += values_removed
                print(f"🧹 Cleaned {col}: removed {values_removed} out-of-range values")
    
    print(f"✅ Data cleaning completed. Total values cleaned: {cleaning_report['values_removed']}")
    return cleaned_df


def write_results_to_las(results_df: pd.DataFrame, 
                        target_log: str, 
                        las_objs: Dict[str, Any], 
                        output_dir: str,
                        include_error: bool = True) -> None:
    """
    Write ML results back to LAS files.
    
    Args:
        results_df: DataFrame with ML results
        target_log: Name of the target log that was predicted
        las_objs: Dictionary of original LAS objects
        output_dir: Output directory for results
        include_error: Whether to include error calculations
    """
    if not LASIO_AVAILABLE:
        print("❌ lasio not available. Cannot write LAS files.")
        return
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Define column names for results
    imputed_col = f"{target_log}_imputed"
    predicted_col = f"{target_log}_pred"
    error_col = f"{target_log}_error"
    
    files_written = 0
    
    for well, las in las_objs.items():
        well_df = results_df[results_df['WELL'] == well]
        if well_df.empty:
            print(f"⚠️ No results found for well {well}")
            continue
        
        try:
            # Create a copy of the original LAS object
            new_las = copy.deepcopy(las)
            
            # Remove existing curves if they exist
            for curve_name in [imputed_col, predicted_col, error_col]:
                if curve_name in new_las.curves:
                    new_las.delete_curve(curve_name)
            
            # Get unit from original target log
            unit = ""
            if target_log in las.curves:
                unit = las.curves[target_log].unit
            
            # Add new curves
            curve_descriptions = [
                (imputed_col, f"ML Imputed {target_log}"),
                (predicted_col, f"ML Predicted {target_log}"),
            ]
            
            if include_error:
                curve_descriptions.append((error_col, f"ML Error {target_log}"))
            
            for curve_name, description in curve_descriptions:
                if curve_name in well_df.columns:
                    # Align data with LAS index (depth)
                    data = well_df.set_index('MD')[curve_name].reindex(new_las.index).values
                    new_las.append_curve(curve_name, data, unit=unit, descr=description)
            
            # Write the new LAS file
            output_path = os.path.join(output_dir, f"{well}_imputed.las")
            new_las.write(output_path, version=2.0)
            print(f"✅ Wrote {output_path}")
            files_written += 1
            
        except Exception as e:
            print(f"❌ Error writing LAS file for well {well}: {e}")
    
    print(f"🎯 Successfully wrote {files_written} LAS files to {output_dir}")


def validate_las_data(df: pd.DataFrame, 
                     required_columns: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Validate LAS data for completeness and quality.
    
    Args:
        df: DataFrame with LAS data
        required_columns: List of required column names
        
    Returns:
        Validation report dictionary
    """
    validation_report = {
        'is_valid': True,
        'issues': [],
        'warnings': [],
        'statistics': {},
        'data_quality_score': 0.0
    }
    
    # Check basic structure
    if df.empty:
        validation_report['is_valid'] = False
        validation_report['issues'].append("DataFrame is empty")
        return validation_report
    
    # Check for required columns
    if required_columns:
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            validation_report['is_valid'] = False
            validation_report['issues'].append(f"Missing required columns: {missing_columns}")
    
    # Check for depth column
    depth_columns = ['MD', 'DEPT', 'DEPTH']
    has_depth = any(col in df.columns for col in depth_columns)
    if not has_depth:
        validation_report['is_valid'] = False
        validation_report['issues'].append("No depth column found")
    
    # Check for well identifier
    if 'WELL' not in df.columns:
        validation_report['warnings'].append("No WELL column found")
    
    # Calculate data quality metrics
    total_cells = df.size
    nan_cells = df.isna().sum().sum()
    data_quality_score = 1.0 - (nan_cells / total_cells) if total_cells > 0 else 0.0
    validation_report['data_quality_score'] = data_quality_score
    
    # Calculate column statistics
    numeric_columns = df.select_dtypes(include=[np.number]).columns
    for col in numeric_columns:
        if col in df.columns:
            validation_report['statistics'][col] = {
                'count': df[col].count(),
                'mean': df[col].mean(),
                'std': df[col].std(),
                'min': df[col].min(),
                'max': df[col].max(),
                'nan_percentage': (df[col].isna().sum() / len(df)) * 100
            }
    
    # Add quality warnings
    if data_quality_score < 0.8:
        validation_report['warnings'].append(f"Low data quality score: {data_quality_score:.2f}")
    
    if 'WELL' in df.columns:
        well_count = df['WELL'].nunique()
        validation_report['statistics']['well_count'] = well_count
        if well_count == 0:
            validation_report['issues'].append("No wells found in data")
    
    return validation_report


def get_las_file_info(file_path: str) -> Dict[str, Any]:
    """
    Get information about a LAS file without loading the full data.
    
    Args:
        file_path: Path to LAS file
        
    Returns:
        Dictionary with file information
    """
    if not LASIO_AVAILABLE:
        return {'error': 'lasio not available'}
    
    try:
        las = lasio.read(file_path)
        
        info = {
            'file_path': file_path,
            'well_name': las.well.WELL.value if las.well.WELL.value else 'Unknown',
            'curve_count': len(las.curves),
            'curve_names': [curve.mnemonic for curve in las.curves],
            'depth_range': (las.index.min(), las.index.max()) if len(las.index) > 0 else (None, None),
            'data_points': len(las.index),
            'version': las.version.VERS.value if las.version.VERS.value else 'Unknown'
        }
        
        return info
        
    except Exception as e:
        return {'error': str(e), 'file_path': file_path}


# Export public interface
__all__ = [
    'load_las_files_from_directory',
    'load_las_files',
    'clean_log_data',
    'write_results_to_las',
    'validate_las_data',
    'get_las_file_info',
    'LASIO_AVAILABLE'
]
