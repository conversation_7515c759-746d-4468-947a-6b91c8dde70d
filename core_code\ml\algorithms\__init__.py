"""
Core ML Algorithms Package

This package contains the fundamental machine learning algorithms used throughout the system:
- regression.py: Multiple Linear Regression utilities and wrappers
- metrics.py: Evaluation metrics for regression and imputation tasks
- preprocessing.py: Advanced data preprocessing and stabilization utilities

These algorithms form the foundation of the ML pipeline and are used by higher-level
components for model training, evaluation, and data processing.
"""

# Import core algorithms with safe fallbacks
try:
    from .regression import MLRModelWrapper, create_mlr_model, validate_mlr_assumptions, MLRPreprocessor
    REGRESSION_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Regression algorithms not available: {e}")
    MLRModelWrapper = None
    create_mlr_model = None
    validate_mlr_assumptions = None
    MLRPreprocessor = None
    REGRESSION_AVAILABLE = False

try:
    from .metrics import (
        cal_r2, cal_cc, evaluate_model_comprehensive,
        calculate_prediction_metrics, calculate_composite_score
    )
    METRICS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Metrics algorithms not available: {e}")
    cal_r2 = None
    cal_cc = None
    evaluate_model_comprehensive = None
    calculate_prediction_metrics = None
    calculate_composite_score = None
    METRICS_AVAILABLE = False

try:
    from .preprocessing import (
        phase1_preprocessing_pipeline,
        enhanced_validate_sequences,
        get_recommended_preprocessing_config,
        numerical_stability_check
    )
    PREPROCESSING_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Advanced preprocessing not available: {e}")
    phase1_preprocessing_pipeline = None
    enhanced_validate_sequences = None
    get_recommended_preprocessing_config = None
    numerical_stability_check = None
    PREPROCESSING_AVAILABLE = False

# Export public interface
__all__ = [
    # Regression
    'MLRModelWrapper',
    'create_mlr_model',
    'validate_mlr_assumptions',
    'MLRPreprocessor',
    
    # Metrics
    'cal_r2',
    'cal_cc',
    'evaluate_model_comprehensive',
    'calculate_prediction_metrics',
    'calculate_composite_score',
    
    # Preprocessing
    'phase1_preprocessing_pipeline',
    'enhanced_validate_sequences',
    'get_recommended_preprocessing_config',
    'numerical_stability_check',
    
    # Status flags
    'REGRESSION_AVAILABLE',
    'METRICS_AVAILABLE',
    'PREPROCESSING_AVAILABLE'
]

print("ML algorithms package initialized")
