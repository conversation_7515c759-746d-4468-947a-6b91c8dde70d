"""
Core ML Utilities Package

This package consolidates all core machine learning functionality for the ML Log Prediction system.
It provides a clean, organized interface to ML algorithms, optimization utilities, and advanced integrations.

Structure:
- algorithms/: Core ML algorithms (regression, metrics, preprocessing)
- optimization/: Performance optimization utilities (GPU, memory, performance monitoring)
- integration/: Advanced ML integrations (Phase 1 enhancements, etc.)

Usage:
    from utils.ml.algorithms import MLRModelWrapper, evaluate_model_comprehensive
    from utils.ml.optimization import GPUAccelerator, MemoryOptimizer
    from utils.ml.integration import impute_logs_deep_phase1_safe
"""

# Import key components for easy access
try:
    from .algorithms.regression import MLRModelWrapper, create_mlr_model
    from .algorithms.metrics import cal_r2, cal_cc
    from .optimization.gpu_utils import GPUAccelerator
    MLR_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Some ML utilities not available: {e}")
    MLRModelWrapper = None
    create_mlr_model = None
    cal_r2 = None
    cal_cc = None
    GPUAccelerator = None
    MLR_AVAILABLE = False

# Version and metadata
__version__ = "1.0.0"
__author__ = "ML Log Prediction System"

# Export public interface
__all__ = [
    # Regression utilities
    'MLRModelWrapper',
    'create_mlr_model',
    
    # Metrics
    'cal_r2',
    'cal_cc',
    
    # Optimization
    'GPUAccelerator',
    
    # Status
    'MLR_AVAILABLE'
]

print("ML utilities package initialized")
