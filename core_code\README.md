# Core Code Package - ML Log Prediction System

This package contains all core machine learning functionality, data processing, and utilities organized in a clean, maintainable structure. This replaces the scattered utilities in the `utils/` directory with a more professional and organized approach.

## 📁 Package Structure

```
core_code/
├── __init__.py                  # Core package interface
├── ml/                          # Machine Learning core
│   ├── __init__.py              # ML package
│   ├── algorithms/              # Core ML algorithms
│   │   ├── __init__.py
│   │   ├── regression.py        # MLR utilities (from utils/mlr_utils.py)
│   │   ├── metrics.py           # Evaluation metrics (from utils/metrics.py)
│   │   └── preprocessing.py     # Advanced preprocessing (planned)
│   ├── models/                  # Model implementations (planned)
│   │   ├── __init__.py
│   │   ├── shallow.py           # Shallow ML models
│   │   └── deep.py              # Deep learning interfaces
│   ├── optimization/            # Performance optimization
│   │   ├── __init__.py
│   │   ├── gpu.py               # GPU acceleration (from utils/optimization.py)
│   │   ├── memory.py            # Memory optimization (planned)
│   │   └── performance.py       # Performance monitoring (planned)
│   └── integration/             # Advanced integrations (planned)
│       ├── __init__.py
│       └── phase1.py            # Phase 1 enhancements
├── data/                        # Data processing core
│   ├── __init__.py
│   ├── loaders.py               # LAS file loading (from utils/data_handler.py)
│   ├── preprocessing.py         # Data preprocessing
│   └── validation.py            # Data validation
└── utils/                       # Core utilities (planned)
    ├── __init__.py
    ├── config.py                # Configuration management
    └── logging.py               # Logging utilities
```

## 🚀 Quick Start

### Basic Usage

```python
# Import from core_code package
from core_code.ml.algorithms import MLRModelWrapper, cal_r2, cal_cc
from core_code.ml.optimization import GPUAccelerator
from core_code.data import load_las_files, normalize_data

# Create and use MLR model
model = MLRModelWrapper(model_type='ridge', enable_diagnostics=True)
model.fit(X_train, y_train)
predictions = model.predict(X_test)

# Use GPU acceleration
gpu = GPUAccelerator()
optimized_model = gpu.optimize_model_for_gpu(pytorch_model)

# Calculate metrics
r2_score = cal_r2(predictions, y_true)
```

### Package-Level Imports

```python
# Import from package level for convenience
from core_code.ml import MLRModelWrapper, GPUAccelerator
from core_code.data import load_las_files
```

## 📊 Available Components

### ✅ Implemented Components

#### ML Algorithms (`core_code.ml.algorithms`)
- **MLRModelWrapper**: Unified interface for Linear, Ridge, Lasso, ElasticNet
- **MLRPreprocessor**: Automated preprocessing with outlier detection and VIF analysis
- **create_mlr_model()**: Factory function for creating MLR models
- **validate_mlr_assumptions()**: Linear regression assumption validation
- **cal_r2()**: R-squared calculation with mask support
- **cal_cc()**: Correlation coefficient calculation
- **evaluate_model_comprehensive()**: Comprehensive model evaluation

#### ML Optimization (`core_code.ml.optimization`)
- **GPUAccelerator**: GPU detection, memory management, model optimization
- **get_gpu_accelerator()**: Factory function for GPU accelerator
- **safe_cuda_empty_cache()**: Safe CUDA cache clearing
- **get_gpu_memory_info()**: GPU memory information

#### Data Processing (`core_code.data`)
- Package structure created (implementations planned)

### 📋 Planned Components

#### ML Models (`core_code.ml.models`)
- **Shallow ML Models**: XGBoost, LightGBM, CatBoost interfaces
- **Deep Learning Models**: SAITS, BRITS, Neural Network interfaces

#### Advanced ML (`core_code.ml.integration`)
- **Phase 1 Enhancements**: Performance optimizations and enhanced preprocessing

#### Data Processing (`core_code.data`)
- **LAS File Loaders**: From utils/data_handler.py
- **Data Preprocessing**: Normalization, sequence creation
- **Data Validation**: Quality checks and validation

## 🔄 Migration from Old Structure

### Migration Benefits

1. **Better Organization**: Related functionality grouped logically
2. **Clear Separation of Concerns**: ML, data, and utilities separated
3. **Professional Structure**: Industry-standard package organization
4. **Easier Maintenance**: Centralized location for each component type
5. **Better Documentation**: Each package has clear purpose and interface
6. **Future Extensibility**: Easy to add new components

### Import Migration Examples

| Old Import | New Import |
|------------|------------|
| `from utils.metrics import cal_r2` | `from core_code.ml.algorithms.metrics import cal_r2` |
| `from utils.optimization import GPUAccelerator` | `from core_code.ml.optimization.gpu import GPUAccelerator` |
| `from utils.mlr_utils import MLRModelWrapper` | `from core_code.ml.algorithms.regression import MLRModelWrapper` |
| `from utils.data_handler import load_las_files` | `from core_code.data.loaders import load_las_files` |

### Backward Compatibility

The main `utils/ml_core.py` has been updated to try importing from `core_code` first, then fall back to the old locations:

```python
# This automatically uses core_code if available, falls back to utils
from utils.ml_core import MODEL_REGISTRY, create_model, train_model
```

## 🎯 Design Principles

1. **Separation of Concerns**: Each package has a single, well-defined responsibility
2. **Dependency Injection**: Components can be easily swapped and tested
3. **Graceful Degradation**: Missing components don't break the entire system
4. **Clear Interfaces**: Each package exports a clean, documented API
5. **Professional Standards**: Follows Python packaging best practices

## 🔧 Development Guidelines

### Adding New Components

1. **Choose the Right Package**:
   - ML algorithms → `core_code/ml/algorithms/`
   - Model implementations → `core_code/ml/models/`
   - Performance optimization → `core_code/ml/optimization/`
   - Data processing → `core_code/data/`
   - Core utilities → `core_code/utils/`

2. **Follow the Pattern**:
   - Create module with clear docstring
   - Add imports to package `__init__.py`
   - Include graceful error handling
   - Add to `__all__` exports

3. **Maintain Compatibility**:
   - Update fallback imports in `utils/ml_core.py`
   - Add deprecation warnings to old locations
   - Update documentation

### Testing New Structure

```python
# Test that imports work correctly
try:
    from core_code.ml.algorithms import MLRModelWrapper, cal_r2
    from core_code.ml.optimization import GPUAccelerator
    print("✅ Core code package working correctly")
except ImportError as e:
    print(f"❌ Import error: {e}")

# Test functionality
model = MLRModelWrapper()
gpu = GPUAccelerator()
print("✅ Core functionality working correctly")
```

## 📈 Implementation Status

### ✅ Phase 1 Complete
- [x] Package structure created
- [x] ML algorithms migrated (regression, metrics)
- [x] GPU optimization migrated
- [x] Backward compatibility implemented
- [x] Documentation created

### 🔄 Phase 2 In Progress
- [ ] Data processing components migration
- [ ] Model implementations organization
- [ ] Advanced preprocessing migration
- [ ] Phase 1 integration migration

### 📋 Phase 3 Planned
- [ ] Memory optimization utilities
- [ ] Performance monitoring utilities
- [ ] Configuration management
- [ ] Logging utilities
- [ ] Complete deprecation of old imports

## 🤝 Contributing

When working with the core_code package:

1. Follow the established structure and patterns
2. Add comprehensive docstrings and type hints
3. Include error handling and graceful fallbacks
4. Update both package `__init__.py` and main documentation
5. Test that both new and old import paths work
6. Add appropriate deprecation warnings

## 📚 See Also

- [utils/ml_core.py](../utils/ml_core.py) - Main ML orchestration (updated to use core_code)
- [models/](../models/) - Advanced model implementations
- [Migration Guide](MIGRATION_GUIDE.md) - Detailed migration instructions (planned)
